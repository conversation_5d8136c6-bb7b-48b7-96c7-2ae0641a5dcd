# JSON 解析錯誤修復測試指南

## 錯誤描述

在新增輔導可預約時間時出現錯誤：
```
"invalid character 'i' looking for beginning of value"
```

## 問題根因

1. **Content-Type 不匹配**: 前端使用 `axiosRequest()` 預設的 "form" 類型，設定 Content-Type 為 "application/x-www-form-urlencoded"
2. **後端期望 JSON**: 後端 API 使用 `c.ShouldBindJSON()` 期望接收 JSON 格式資料
3. **資料格式衝突**: 表單格式的資料無法被 JSON 解析器正確處理

## 修復內容

### 1. 前端修復
**檔案**: `templates/backend/counsel/counsel.available_time_reg.tmpl`

#### 1.1 修改 axiosRequest 調用
```javascript
// 修復前
axiosRequest()[method](url, submitData)

// 修復後
axiosRequest('json')[method](url, submitData)
```

#### 1.2 優化提交資料結構
```javascript
// 修復前 - 傳送整個 data 物件
axiosRequest()[method](url, this.data)

// 修復後 - 只傳送必要欄位
const submitData = {
    date: this.data.date,
    time_slot_options: this.data.time_slot_options,
    status: this.data.status,
    note: this.data.note
}
axiosRequest('json')[method](url, submitData)
```

### 2. 後端 API 確認
**檔案**: `app/controllers/api/backend_api/counsel.go`

- ✅ 使用 `c.ShouldBindJSON()` 正確處理 JSON 請求
- ✅ 正確的請求結構定義
- ✅ 適當的錯誤處理

## 測試步驟

### 第一步：新增功能測試

#### 1.1 基本新增測試
1. 訪問 `/admin/counsel/available-times/reg`
2. 填寫表單：
   - 日期：2025-08-19
   - 時間段：09:00-12:00, 14:00-17:00
   - 狀態：啟用
   - 備註：測試資料
3. 提交表單
4. **預期結果**: 成功創建，不再出現 JSON 解析錯誤

#### 1.2 複雜時間段測試
1. 新增可預約時間
2. 添加多個時間段：
   - 09:00-12:00
   - 13:00-15:00
   - 16:00-18:00
3. 提交表單
4. **預期結果**: 成功保存所有時間段

### 第二步：編輯功能測試

#### 2.1 編輯現有資料
1. 編輯已存在的可預約時間
2. 修改日期和時間段
3. 提交表單
4. **預期結果**: 成功更新，不出現錯誤

#### 2.2 時間段修改測試
1. 編輯可預約時間
2. 新增、移除、修改時間段
3. 提交表單
4. **預期結果**: 變更正確保存

### 第三步：錯誤處理測試

#### 3.1 必填欄位驗證
1. 嘗試提交空白日期
2. **預期結果**: 顯示適當的錯誤訊息

#### 3.2 時間格式驗證
1. 設定無效的時間段（開始時間晚於結束時間）
2. **預期結果**: 前端驗證阻止提交

#### 3.3 重疊時間段驗證
1. 設定重疊的時間段
2. **預期結果**: 顯示重疊警告，阻止提交

### 第四步：API 直接測試

#### 4.1 新增 API 測試
```bash
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2025-08-19",
    "time_slot_options": [
      {"start": "09:00", "end": "12:00"},
      {"start": "14:00", "end": "17:00"}
    ],
    "status": "Y",
    "note": "API 測試"
  }'
```

**預期回應**:
```json
{
  "msg": "建立成功",
  "id": 123
}
```

#### 4.2 更新 API 測試
```bash
curl -X PATCH "/api/admin/counsel-available-times/123" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2025-08-20",
    "time_slot_options": [
      {"start": "10:00", "end": "13:00"},
      {"start": "15:00", "end": "18:00"}
    ],
    "status": "Y",
    "note": "API 更新測試"
  }'
```

**預期回應**:
```json
{
  "msg": "更新成功"
}
```

### 第五步：瀏覽器開發者工具檢查

#### 5.1 網路請求檢查
1. 開啟瀏覽器開發者工具
2. 切換到 Network 標籤
3. 提交表單
4. **檢查項目**:
   - Request Headers 中 Content-Type 為 "application/json"
   - Request Payload 為正確的 JSON 格式
   - Response 狀態碼為 200

#### 5.2 請求內容驗證
**正確的請求格式**:
```json
{
  "date": "2025-08-19",
  "time_slot_options": [
    {"start": "09:00", "end": "12:00"},
    {"start": "14:00", "end": "17:00"}
  ],
  "status": "Y",
  "note": "測試備註"
}
```

## 常見問題排除

### 問題 1：仍然出現 JSON 解析錯誤
**檢查**:
- 確認前端使用 `axiosRequest('json')`
- 檢查 submitData 物件結構是否正確
- 確認沒有傳送額外的非 JSON 欄位

### 問題 2：Content-Type 不正確
**檢查**:
- 瀏覽器開發者工具中的 Request Headers
- 確認 axiosRequest 函數的參數正確

### 問題 3：資料格式錯誤
**檢查**:
- time_slot_options 是否為正確的陣列格式
- 日期格式是否為 "YYYY-MM-DD"
- 所有必填欄位是否都有值

## 成功指標

- [ ] 新增可預約時間成功，無 JSON 錯誤
- [ ] 編輯可預約時間成功，無 JSON 錯誤
- [ ] 網路請求使用正確的 Content-Type
- [ ] 請求資料為正確的 JSON 格式
- [ ] 後端正確接收和處理資料
- [ ] 時間段重疊檢測功能正常
- [ ] 所有驗證邏輯正常運作

## 技術說明

### axiosRequest 函數類型
- `axiosRequest()` 或 `axiosRequest('form')`: Content-Type 為 "application/x-www-form-urlencoded"
- `axiosRequest('json')`: Content-Type 為 "application/json"
- `axiosRequest('form-data')`: Content-Type 為 "multipart/form-data"

### 後端綁定方法
- `c.ShouldBind()`: 自動根據 Content-Type 選擇綁定方式
- `c.ShouldBindJSON()`: 專門處理 JSON 格式
- `c.ShouldBindForm()`: 專門處理表單格式

這次修復確保了前後端的資料格式完全匹配，解決了 JSON 解析錯誤問題。
