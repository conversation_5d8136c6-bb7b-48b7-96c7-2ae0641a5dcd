# 輔導預約時間管理系統測試指南

## 系統概述

本次更新新增了輔導預約時間的後台控管系統，讓管理人員可以設定可預約的日期和時間範圍，並在前端實現智能的時間衝突檢測。

## 主要功能

### 1. 後台時間管理
- 設定可預約日期
- 為每個日期設定多個時間段
- 檢視該日期已有的預約
- 時間衝突提醒

### 2. 前端智能預約
- 只顯示可預約的日期
- 動態載入可用時間
- 自動排除已預約時間
- 防止時間重疊

### 3. 衝突檢測
- 預約時間不可超出設定範圍
- 自動檢測與現有預約的衝突
- 後台調整時的衝突提醒

## 測試步驟

### 第一步：資料庫遷移
```bash
# 執行遷移
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" up

# 檢查新表格
mysql -u username -p database_name -e "DESCRIBE counsel_available_times;"
```

### 第二步：後台時間管理測試

#### 2.1 可預約時間列表
1. 訪問 `/admin/counsel/available-times`
2. 檢查是否顯示預設的可預約時間
3. 測試搜尋功能（日期範圍、狀態）
4. 測試分頁功能

#### 2.2 新增可預約時間
1. 點擊「新增可預約時間」
2. 選擇日期
3. 設定時間段：
   - 測試新增多個時間段
   - 測試移除時間段
   - 驗證開始時間必須早於結束時間
4. 設定狀態和備註
5. 提交並檢查是否成功

#### 2.3 編輯可預約時間
1. 編輯現有的可預約時間
2. 修改時間段
3. 檢查是否顯示該日期已有的預約
4. 測試時間衝突提醒功能

#### 2.4 時間衝突檢測
1. 先創建一個預約
2. 編輯該日期的可預約時間
3. 設定與預約重疊的時間段
4. 檢查是否顯示衝突警告

### 第三步：前端預約功能測試

#### 3.1 日期選擇
1. 訪問 `/member/counsel`
2. 點擊「直接預約輔導」
3. 檢查日期下拉選單：
   - 只顯示後台設定的可預約日期
   - 不顯示過去日期
   - 顯示日期備註（如果有）

#### 3.2 時間選擇
1. 選擇輔導項目和時長
2. 選擇預約日期
3. 檢查時間下拉選單：
   - 只顯示可用時間
   - 顯示時間範圍（開始-結束）
   - 自動排除已預約時間

#### 3.3 智能時間過濾
1. 選擇較長的輔導時長（如120分鐘）
2. 檢查可用時間是否正確過濾
3. 測試不同時長下的可用時間變化

#### 3.4 預約提交
1. 完整填寫預約表單
2. 提交預約
3. 檢查是否成功創建
4. 再次嘗試預約相同時間，應該被拒絕

### 第四步：衝突檢測測試

#### 4.1 前端衝突檢測
1. 創建一個預約（如 14:00-15:00）
2. 嘗試預約重疊時間（如 14:30-15:30）
3. 應該顯示衝突錯誤訊息

#### 4.2 後台衝突提醒
1. 在後台編輯有預約的日期
2. 修改時間段使其與預約重疊
3. 檢查是否顯示衝突警告
4. 仍可以保存（管理員有調整權限）

#### 4.3 邊界情況測試
1. 測試相鄰時間（如 14:00-15:00 和 15:00-16:00）
2. 測試跨時間段的預約
3. 測試最小時間間隔（15分鐘）

### 第五步：API 測試

#### 5.1 獲取可預約日期
```bash
curl -X GET "/api/members/counsels/available-dates" \
  -H "Authorization: Bearer <token>"
```

#### 5.2 獲取指定日期可用時間
```bash
curl -X GET "/api/members/counsels/available-times/2024-01-15" \
  -H "Authorization: Bearer <token>"
```

#### 5.3 後台時間管理 API
```bash
# 獲取可預約時間列表
curl -X GET "/api/admin/counsel-available-times"

# 新增可預約時間
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2024-01-20",
    "time_slot_options": [
      {"start": "09:00", "end": "12:00"},
      {"start": "14:00", "end": "17:00"}
    ],
    "status": "Y",
    "note": "測試時間"
  }'
```

## 預期結果

### 成功指標
- [ ] 後台可以管理可預約時間
- [ ] 前端只顯示可預約日期
- [ ] 時間選擇智能過濾
- [ ] 衝突檢測正常運作
- [ ] 預約成功後時間被鎖定
- [ ] 後台衝突提醒功能正常

### 錯誤處理
- [ ] 無可用時間時的友善提示
- [ ] 時間衝突的清楚錯誤訊息
- [ ] 表單驗證正確運作

## 常見問題排除

### 問題 1：前端無可用日期
**原因**：後台未設定可預約時間或狀態為停用
**解決**：檢查後台設定，確保有啟用的可預約時間

### 問題 2：時間選項為空
**原因**：時間段設定錯誤或與現有預約全部衝突
**解決**：檢查時間段設定和現有預約情況

### 問題 3：衝突檢測失效
**原因**：資料庫查詢邏輯錯誤
**解決**：檢查 CheckAppointmentConflict 函數邏輯

## 效能考量

1. **時間計算**：前端時間計算較多，但都是輕量級操作
2. **API 調用**：日期變更時會調用 API，考慮添加快取
3. **資料庫查詢**：衝突檢測需要查詢現有預約，已優化查詢條件

## 安全考量

1. **權限控制**：確保只有管理員可以設定可預約時間
2. **資料驗證**：前後端都有時間格式和邏輯驗證
3. **併發控制**：預約提交時的資料庫鎖定機制

## 後續優化建議

1. **批量設定**：支援批量設定多個日期的可預約時間
2. **模板功能**：提供常用時間段模板
3. **假日管理**：整合假日資料，自動排除假日
4. **通知功能**：預約成功後的通知機制
5. **統計報表**：可預約時間的使用率統計
