# 輔導作業表格功能增強總結

## 🎯 修改目標

參考課程作業表格的結構，為輔導作業表格添加缺少的欄位，包括下載功能、上傳時間顯示，並將狀態修改為審核狀態選擇器，讓管理人員可以批改作業。

## ✅ 已完成的修改

### 1. 前端表格結構優化

#### 表格標題欄位調整
```html
<!-- 修改前 -->
<th width="25%">作業名稱</th>
<th width="35%">作業說明</th>
<th width="15%">截止時間</th>
<th width="10%">狀態</th>
<th width="5%">操作</th>

<!-- 修改後 -->
<th width="20%">作業名稱</th>
<th width="25%">作業說明</th>
<th width="12%">截止時間</th>
<th width="8%">下載(作業)</th>
<th width="8%">下載(學員)</th>
<th width="12%">上傳時間</th>
<th width="10%">審核狀態</th>
<th width="5%">操作</th>
```

#### 新增的欄位功能

**1. 作業名稱欄位增強**
- 顯示作業標題或預設「輔導作業」
- 添加作業來源標示（預設作業/額外作業）

**2. 下載(作業檔案)欄位**
- 顯示作業檔案下載按鈕
- 自動生成下載檔名：`{輔導標題}_{作業標題}.{副檔名}`
- 無檔案時顯示「無檔案」

**3. 下載(學員上傳)欄位**
- 顯示學員上傳檔案下載按鈕
- 自動生成下載檔名：`{輔導標題}_{作業標題}_學員上傳.{副檔名}`
- 未上傳時顯示「未上傳」

**4. 上傳時間欄位**
- 顯示學員上傳作業的時間
- 格式：`YYYY/MM/DD HH:mm`
- 未上傳時顯示「未上傳」

**5. 審核狀態欄位（智能顯示）**
- **未上傳時**：顯示狀態標籤
  - 已逾期：紅色標籤「已逾期」
  - 待繳交：黃色標籤「待繳交」
- **已上傳時**：顯示審核狀態選擇器
  - 已繳交（藍色）
  - 已通過（綠色）
  - 需修改（紅色）

### 2. JavaScript 功能增強

#### 新增的方法

**1. updateCounselWorkStatus(counselWork)**
```javascript
// 更新輔導作業審核狀態
updateCounselWorkStatus(counselWork) {
    const originalStatus = counselWork.status
    
    const statusData = {
        status: counselWork.status
    }
    
    axiosRequest('json')
        .patch(`/api/admin/member-counsel-works/${counselWork.id}/status`, statusData)
        .then(res => {
            msgTopSuccess('審核狀態更新成功')
        })
        .catch(err => {
            console.log(err)
            msgError(err.response?.data?.msg || '狀態更新失敗')
            // 如果更新失敗，恢復原狀態
            counselWork.status = originalStatus
        })
}
```

**2. getCounselWorkFileDownloadName(counselWork)**
```javascript
// 獲取輔導作業檔案下載名稱
getCounselWorkFileDownloadName(counselWork) {
    if (!counselWork.work_file) return ''
    const extension = counselWork.work_file.split('.').pop()
    const workTitle = counselWork.work_title || '輔導作業'
    return `${counselWork.counsel_title}_${workTitle}.${extension}`
}
```

**3. getCounselUploadFileDownloadName(counselWork)**
```javascript
// 獲取學員上傳檔案下載名稱
getCounselUploadFileDownloadName(counselWork) {
    if (!counselWork.upload_file) return ''
    const extension = counselWork.upload_file.split('.').pop()
    const workTitle = counselWork.work_title || '輔導作業'
    return `${counselWork.counsel_title}_${workTitle}_學員上傳.${extension}`
}
```

### 3. 後端 API 新增

#### 新增狀態更新 API
**檔案**：`app/controllers/api/backend_api/counsel_appointment_work.go`

**函數**：`UpdateMemberCounselWorkStatus(c *gin.Context)`

**功能**：
- 更新會員輔導作業的審核狀態
- 驗證狀態值的有效性
- 記錄更新者和更新時間

**API 端點**：`PATCH /api/admin/member-counsel-works/{id}/status`

**請求格式**：
```json
{
    "status": "approved"  // pending, submitted, approved, rejected
}
```

**狀態值說明**：
- `pending`: 待繳交
- `submitted`: 已繳交
- `approved`: 已通過
- `rejected`: 需修改

#### 路由設定
**檔案**：`routes/backend_routes.go`

```go
// 會員輔導作業狀態更新路由
memberCounselWorks := api.Group("/member-counsel-works")
memberCounselWorks.PATCH("/:id/status", UpdateMemberCounselWorkStatus)
```

## 🎨 使用者體驗改善

### 1. 視覺設計優化
- **一致性**：與課程作業表格保持相同的設計風格
- **清晰度**：明確區分不同類型的操作按鈕
- **狀態指示**：使用顏色編碼清楚表示不同狀態

### 2. 互動體驗提升
- **即時反饋**：狀態更新後立即顯示結果
- **錯誤處理**：更新失敗時自動恢復原狀態
- **檔案下載**：一鍵下載，自動命名

### 3. 功能完整性
- **檔案管理**：支援作業檔案和學員上傳檔案的下載
- **狀態追蹤**：完整的作業生命週期狀態管理
- **時間記錄**：準確記錄上傳時間和截止時間

## 🔧 技術實現特點

### 1. 智能狀態顯示
```html
<!-- 根據上傳狀態智能切換顯示方式 -->
<template v-if="!counselWork.upload_file">
    <!-- 未上傳：顯示狀態標籤 -->
    <span v-if="isDateExpired(counselWork.deadline_at)" class="badge bg-danger">已逾期</span>
    <span v-else class="badge bg-warning">待繳交</span>
</template>
<template v-else>
    <!-- 已上傳：顯示審核選擇器 -->
    <select v-model="counselWork.status" @change="updateCounselWorkStatus(counselWork)">
        <option value="submitted" style="color: #0d6efd;">已繳交</option>
        <option value="approved" style="color: #198754;">已通過</option>
        <option value="rejected" style="color: #dc3545;">需修改</option>
    </select>
</template>
```

### 2. 檔案下載優化
- **路徑處理**：自動添加 `/` 前綴處理檔案路徑
- **檔名生成**：結合輔導標題和作業標題生成有意義的檔名
- **類型區分**：清楚區分作業檔案和學員上傳檔案

### 3. 錯誤處理機制
- **狀態回滾**：API 調用失敗時自動恢復原狀態
- **友善提示**：提供清楚的成功和錯誤訊息
- **參數驗證**：後端嚴格驗證狀態值的有效性

## 📊 功能對比

### 修改前的限制
- ❌ 無法下載作業檔案
- ❌ 無法下載學員上傳檔案
- ❌ 看不到上傳時間
- ❌ 無法進行作業審核
- ❌ 狀態顯示過於簡單

### 修改後的優勢
- ✅ 完整的檔案下載功能
- ✅ 清楚的時間資訊顯示
- ✅ 靈活的審核狀態管理
- ✅ 智能的狀態顯示邏輯
- ✅ 與課程作業一致的體驗

## 📋 測試檢查清單

### 1. 前端功能測試
- [ ] 表格正確顯示所有新欄位
- [ ] 作業檔案下載功能正常
- [ ] 學員上傳檔案下載功能正常
- [ ] 上傳時間正確顯示
- [ ] 審核狀態選擇器正常運作

### 2. 後端 API 測試
- [ ] 狀態更新 API 正常運作
- [ ] 狀態值驗證正確
- [ ] 錯誤處理機制有效
- [ ] 更新記錄正確保存

### 3. 整合測試
- [ ] 前後端狀態同步正確
- [ ] 錯誤處理和狀態回滾正常
- [ ] 檔案下載名稱正確生成
- [ ] 權限控制正常運作

### 4. 使用者體驗測試
- [ ] 操作流程直觀易懂
- [ ] 視覺反饋及時準確
- [ ] 錯誤訊息清楚明確
- [ ] 響應式設計正常

## 🚀 改善效果

### 1. 管理效率提升
- **批改作業**：管理員可以直接在表格中進行作業審核
- **檔案管理**：一鍵下載作業檔案和學員上傳檔案
- **狀態追蹤**：清楚掌握每個作業的進度和狀態

### 2. 資訊完整性
- **時間記錄**：完整的上傳時間和截止時間資訊
- **檔案追蹤**：清楚區分作業檔案和學員上傳檔案
- **狀態歷程**：完整的作業狀態變更記錄

### 3. 使用體驗一致性
- **設計統一**：與課程作業表格保持一致的設計風格
- **操作習慣**：相同的操作邏輯和互動方式
- **功能對等**：提供與課程作業相同水準的管理功能

這次修改成功地將輔導作業表格提升到與課程作業表格相同的功能水準，為管理員提供了完整的作業管理工具。
