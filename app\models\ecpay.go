package models

import (
	"time"

	"gorm.io/gorm"
)

// EcpayOrder 綠界金流交易記錄
type EcpayOrder struct {
	ID              uint           `gorm:"primary_key" form:"id" json:"id"`
	OrderNo         string         `gorm:"unique" form:"order_no" json:"order_no"`
	RtnCode         string         `form:"rtn_code" json:"rtn_code"`
	RtnMsg          string         `form:"rtn_msg" json:"rtn_msg"`
	MerchantID      string         `form:"merchant_id" json:"merchant_id"`
	MerchantTradeNo string         `form:"merchant_trade_no" json:"merchant_trade_no"`
	TradeNo         string         `form:"trade_no" json:"trade_no"`
	TradeAmt        int            `form:"trade_amt" json:"trade_amt"`
	PaymentDate     string         `form:"payment_date" json:"payment_date"`
	PaymentType     string         `form:"payment_type" json:"payment_type"`
	PaymentTypeChargeFee string    `form:"payment_type_charge_fee" json:"payment_type_charge_fee"`
	TradeDate       string         `form:"trade_date" json:"trade_date"`
	SimulatePaid    string         `form:"simulate_paid" json:"simulate_paid"`
	CheckMacValue   string         `form:"check_mac_value" json:"check_mac_value"`
	CreatedAt       time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt       time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

func (EcpayOrder) TableName() string {
	return "ecpay_orders"
}
