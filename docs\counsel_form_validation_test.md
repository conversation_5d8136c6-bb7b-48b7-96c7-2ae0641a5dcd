# 輔導時間設定表單驗證測試指南

## 修改內容

已修正 `counsel.available_time_reg.tmpl` 中的 `submitData` 方法，根據不同類型（固定日期 vs 常駐設定）進行相應的驗證。

### 主要修改

#### 1. 分離驗證邏輯
```javascript
// 修改前：只檢查日期（適用於固定日期）
if (!this.data.date) {
    msgError('請選擇日期')
    return
}

// 修改後：根據類型進行不同驗證
if (this.data.type === 'specific') {
    // 固定日期設定的驗證
    if (!this.data.date) {
        msgError('請選擇日期')
        return
    }
} else if (this.data.type === 'recurring') {
    // 常駐設定的驗證
    if (this.data.day_of_week === null || this.data.day_of_week === undefined) {
        msgError('請選擇星期')
        return
    }
}
```

#### 2. 共同驗證邏輯
保留了時間段相關的共同驗證：
- 至少設定一個時間段
- 時間段格式完整性
- 開始時間早於結束時間
- 時段不重疊檢查

## 測試步驟

### 第一步：固定日期設定驗證測試

#### 1.1 新增固定日期設定
1. 訪問 `/admin/counsel/available-times/reg`
2. 選擇類型：「固定日期」
3. **測試案例**：
   - 不選擇日期，直接提交 → 應顯示「請選擇日期」
   - 選擇日期但不設定時間段 → 應顯示「請至少設定一個時間段」
   - 設定重疊時間段 → 應顯示「時間段不能重疊，請調整時間設定」
   - 設定開始時間晚於結束時間 → 應顯示「開始時間必須早於結束時間」

#### 1.2 編輯固定日期設定
1. 編輯現有的固定日期設定
2. 修改日期為空 → 應顯示「請選擇日期」
3. 正常修改並提交 → 應成功更新

### 第二步：常駐設定驗證測試

#### 2.1 新增常駐設定
1. 訪問 `/admin/counsel/available-times/reg`
2. 選擇類型：「常駐設定」
3. **測試案例**：
   - 不選擇星期，直接提交 → 應顯示「請選擇星期」
   - 選擇星期但不設定時間段 → 應顯示「請至少設定一個時間段」
   - 設定重疊時間段 → 應顯示「時間段不能重疊，請調整時間設定」
   - 設定開始時間晚於結束時間 → 應顯示「開始時間必須早於結束時間」

#### 2.2 編輯常駐設定
1. 編輯現有的常駐設定
2. 修改星期選擇 → 應正常更新
3. 正常修改並提交 → 應成功更新

### 第三步：類型切換測試

#### 3.1 動態欄位顯示
1. 新增設定時，預設選擇「固定日期」
2. 檢查日期欄位顯示，星期欄位隱藏
3. 切換到「常駐設定」
4. 檢查星期欄位顯示，日期欄位隱藏

#### 3.2 驗證規則切換
1. 選擇「固定日期」，不填日期，提交 → 應顯示日期錯誤
2. 切換到「常駐設定」，不選星期，提交 → 應顯示星期錯誤
3. 切換回「固定日期」，填入日期，提交 → 應通過驗證

### 第四步：表單驗證規則測試

#### 4.1 HTML5 驗證
1. 檢查 `required` 屬性根據類型動態設定
2. 固定日期：日期欄位有 `:required="data.type === 'specific'"`
3. 常駐設定：星期欄位有 `:required="data.type === 'recurring'"`

#### 4.2 jQuery 驗證
1. 檢查 jQuery validate 規則正確設定
2. 類型欄位：必填
3. 日期欄位：當類型為 'specific' 時必填
4. 星期欄位：當類型為 'recurring' 時必填

### 第五步：錯誤訊息測試

#### 5.1 類型相關錯誤
- 固定日期未選擇：「請選擇日期」
- 常駐設定未選擇：「請選擇星期」
- 類型未選擇：「請選擇類型」

#### 5.2 時間段相關錯誤
- 無時間段：「請至少設定一個時間段」
- 時間段不完整：「請完整填寫所有時間段」
- 時間順序錯誤：「開始時間必須早於結束時間」
- 時段重疊：「時間段不能重疊，請調整時間設定」

### 第六步：資料提交測試

#### 6.1 固定日期提交
```javascript
// 預期提交資料格式
{
    "type": "specific",
    "date": "2025-08-25",
    "time_slot_options": [
        {"start": "09:00", "end": "12:00"},
        {"start": "14:00", "end": "17:00"}
    ],
    "status": "Y",
    "note": "特殊活動日"
}
```

#### 6.2 常駐設定提交
```javascript
// 預期提交資料格式
{
    "type": "recurring",
    "day_of_week": 0,
    "time_slot_options": [
        {"start": "10:00", "end": "16:00"}
    ],
    "status": "Y",
    "note": "星期日特殊時間"
}
```

### 第七步：邊界情況測試

#### 7.1 資料載入測試
1. 編輯現有固定日期設定 → 類型應為 'specific'，日期欄位顯示
2. 編輯現有常駐設定 → 類型應為 'recurring'，星期欄位顯示

#### 7.2 類型切換後的資料保持
1. 設定固定日期和時間段
2. 切換到常駐設定 → 時間段應保持
3. 切換回固定日期 → 日期和時間段應保持

## 驗證重點

### 1. 驗證邏輯正確性
- [ ] 固定日期類型只驗證日期，不驗證星期
- [ ] 常駐設定類型只驗證星期，不驗證日期
- [ ] 共同驗證邏輯（時間段）對兩種類型都生效

### 2. 使用者體驗
- [ ] 錯誤訊息清楚明確
- [ ] 欄位動態顯示/隱藏正常
- [ ] 類型切換流暢

### 3. 資料完整性
- [ ] 提交的資料格式正確
- [ ] 根據類型包含相應的欄位
- [ ] 不包含不相關的欄位

### 4. 表單驗證一致性
- [ ] HTML5 驗證與 JavaScript 驗證一致
- [ ] jQuery 驗證規則正確
- [ ] 錯誤訊息與驗證規則對應

## 成功指標

- [ ] 固定日期設定的驗證只檢查日期相關欄位
- [ ] 常駐設定的驗證只檢查星期相關欄位
- [ ] 時間段驗證對兩種類型都正常運作
- [ ] 錯誤訊息準確反映驗證失敗的原因
- [ ] 類型切換時驗證規則正確調整
- [ ] 資料提交格式符合後端 API 期望

這個修改確保了表單驗證邏輯根據不同的設定類型進行相應的檢查，提供了更準確的使用者回饋和更好的使用者體驗。
