# 輔導預約系統優化部署指南

## 部署前檢查清單

### 1. 程式碼檢查
- [ ] 所有新增的檔案都已提交
- [ ] 沒有編譯錯誤
- [ ] 程式碼已經過 code review

### 2. 資料庫準備
- [ ] 備份現有資料庫
- [ ] 確認遷移檔案正確
- [ ] 測試環境驗證通過

### 3. 前端資源
- [ ] 新增的模板檔案已上傳
- [ ] CSS/JS 檔案已更新
- [ ] 靜態資源已部署

## 部署步驟

### 第一步：資料庫遷移
```bash
# 1. 備份資料庫
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 執行遷移
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" up

# 3. 驗證新表格
mysql -u username -p database_name -e "DESCRIBE counsel_types;"
mysql -u username -p database_name -e "DESCRIBE counsel_appointments;" | grep -E "(counsel_type_id|appointment_date|appointment_time|requested_duration)"
```

### 第二步：部署後端程式
```bash
# 1. 停止服務
sudo systemctl stop grace-backend

# 2. 更新程式碼
git pull origin main

# 3. 編譯程式
go build -o grace-backend

# 4. 啟動服務
sudo systemctl start grace-backend

# 5. 檢查服務狀態
sudo systemctl status grace-backend
```

### 第三步：部署前端資源
```bash
# 1. 上傳新的模板檔案
# templates/backend/counsel/counsel.types.tmpl
# templates/backend/counsel/counsel.type_reg.tmpl
# templates/front/member/member.counsel.tmpl (已修改)

# 2. 清除快取（如果有使用）
# 根據實際快取機制執行相應命令
```

### 第四步：驗證部署

#### 4.1 後台功能驗證
1. 登入後台管理系統
2. 訪問 `/admin/counsel/types` 檢查輔導項目管理頁面
3. 測試新增、編輯、刪除輔導項目
4. 檢查 `/admin/counsel/index` 是否正常顯示

#### 4.2 前端功能驗證
1. 登入會員系統
2. 訪問 `/member/counsel` 檢查預約頁面
3. 測試「直接預約輔導」功能
4. 確認原有「課程輔導預約」功能正常

#### 4.3 API 功能驗證
```bash
# 測試獲取輔導項目 API
curl -X GET "https://your-domain.com/api/members/counsels/types" \
  -H "Authorization: Bearer <valid-token>"

# 測試後台輔導項目管理 API
curl -X GET "https://your-domain.com/api/admin/counsel-types" \
  -H "Authorization: Bearer <admin-token>"
```

## 監控要點

### 1. 系統日誌
監控以下日誌中的錯誤：
- 應用程式日誌
- 資料庫查詢日誌
- Web 伺服器存取日誌

### 2. 效能指標
- 頁面載入時間
- API 回應時間
- 資料庫查詢效能

### 3. 功能監控
- 預約成功率
- 錯誤率
- 使用者回饋

## 回滾程序

如果發現嚴重問題需要回滾：

### 1. 緊急回滾
```bash
# 1. 停止服務
sudo systemctl stop grace-backend

# 2. 回滾程式碼
git checkout <previous-commit-hash>
go build -o grace-backend

# 3. 回滾資料庫（如果需要）
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" down 2

# 4. 恢復資料庫備份（最後手段）
mysql -u username -p database_name < backup_YYYYMMDD_HHMMSS.sql

# 5. 重啟服務
sudo systemctl start grace-backend
```

### 2. 回滾驗證
- 確認原有功能正常運作
- 檢查資料完整性
- 通知相關人員

## 部署後任務

### 1. 文件更新
- [ ] 更新使用者手冊
- [ ] 更新 API 文件
- [ ] 更新系統架構文件

### 2. 使用者通知
- [ ] 通知管理員新功能
- [ ] 準備使用者教學材料
- [ ] 設定客服支援

### 3. 持續監控
- [ ] 設定監控告警
- [ ] 定期檢查系統狀態
- [ ] 收集使用者回饋

## 聯絡資訊

如果部署過程中遇到問題，請聯絡：
- 技術負責人：[姓名] [聯絡方式]
- 系統管理員：[姓名] [聯絡方式]
- 緊急聯絡人：[姓名] [聯絡方式]

## 附錄

### A. 新增的檔案清單
- `database/migrations/000224_create_counsel_types_table.up.mysql`
- `database/migrations/000224_create_counsel_types_table.down.mysql`
- `database/migrations/000225_add_direct_booking_fields_to_counsel_appointments.up.mysql`
- `database/migrations/000225_add_direct_booking_fields_to_counsel_appointments.down.mysql`
- `templates/backend/counsel/counsel.types.tmpl`
- `templates/backend/counsel/counsel.type_reg.tmpl`

### B. 修改的檔案清單
- `app/models/counsel.go`
- `app/controllers/api/front_api/counsel.go`
- `app/controllers/api/backend_api/counsel.go`
- `app/controllers/backend/counsel.go`
- `routes/front_routes.go`
- `routes/backend_routes.go`
- `templates/front/member/member.counsel.tmpl`
