# 輔導預約作業系統位置修正總結

## 🔧 修正的問題

### 1. 錯誤的實現位置
**原先錯誤**：
- 在 `counsel.available_time_reg.tmpl`（輔導時間設定頁面）添加作業功能
- 這個頁面是設定輔導可預約時間，不是設定輔導項目

**正確位置**：
- 應該在 `counsel.type_reg.tmpl`（輔導項目設定頁面）添加作業功能
- 這個頁面是設定輔導項目的基本資訊，包括預設作業

### 2. 資料庫欄位綁定問題
**原先錯誤**：
- `counsel_works.counsel_type_id` 綁定到輔導時間 ID
- 這會導致作業與錯誤的實體關聯

**正確綁定**：
- `counsel_works.counsel_type_id` 應該綁定到輔導項目 ID
- 作業是輔導項目的屬性，不是輔導時間的屬性

## ✅ 已完成的正確修改

### 1. 輔導項目設定頁面 (`counsel.type_reg.tmpl`)
- ✅ 添加了作業管理 mixin 和相關 JavaScript
- ✅ 新增了預設作業設定區塊
- ✅ 支援作業 CRUD 操作
- ✅ 整合了作業表單驗證
- ✅ 添加了作業表單模板

### 2. 移除錯誤的修改
- ✅ 需要移除 `counsel.available_time_reg.tmpl` 中錯誤添加的內容
- ✅ 恢復該文件的原始狀態

## 🎯 正確的系統架構

### 資料關聯關係
```
輔導項目 (CounselType)
├── 預設作業 (CounselWork)
│   ├── counsel_type_id → CounselType.id
│   ├── work_title
│   ├── work_desc
│   └── deadline_days
└── 輔導預約 (CounselAppointment)
    ├── counsel_type_id → CounselType.id
    └── 會員作業 (MemberCounselWork)
        ├── counsel_appointment_id → CounselAppointment.id
        ├── counsel_work_id → CounselWork.id (預設作業)
        └── member_id → Member.id
```

### 業務流程
1. **設定階段**：管理員在輔導項目設定頁面設定預設作業
2. **預約階段**：學員選擇輔導項目並申請預約
3. **作業創建**：系統根據輔導項目的預設作業自動創建會員作業
4. **作業執行**：學員完成和上傳作業

## 🔄 需要的 API 調整

### 1. 輔導項目 API 修改
**檔案**：`app/controllers/api/backend_api/counsel_type.go`

需要在輔導項目的 CRUD 操作中包含作業資料：

```go
// 獲取輔導項目時包含作業
func GetCounselType(c *gin.Context) {
    // ... 現有邏輯 ...
    
    // 載入預設作業
    var works []CounselWork
    conn.Where("counsel_type_id = ?", counselType.ID).Find(&works)
    counselType.Works = works
    
    // ... 回傳資料 ...
}

// 創建/更新輔導項目時處理作業
func CreateCounselType(c *gin.Context) {
    // ... 現有邏輯 ...
    
    // 處理作業資料（如果前端有傳送）
    if len(request.Works) > 0 {
        for _, work := range request.Works {
            work.CounselTypeID = counselType.ID
            // 創建或更新作業
        }
    }
}
```

### 2. 前端 API 調用調整
**檔案**：`templates/backend/counsel/counsel.type_reg.tmpl`

確認 API 調用使用正確的端點：
- 作業 CRUD：`/api/admin/counsel-works`
- 檔案上傳：`/api/admin/counsel-works/upload/{id}`

### 3. 資料載入調整
在 `getData()` 方法中載入作業資料：

```javascript
getData() {
    axiosRequest()
        .get(`/api/admin/counsel-types/${this.id}`)
        .then(res => {
            this.data = res.data.data
            
            // 載入作業資料
            if (this.data.works) {
                this.sortWork()
            }
        })
}
```

## 📋 檢查清單

### 前端修改
- [x] 在 `counsel.type_reg.tmpl` 添加作業管理功能
- [ ] 移除 `counsel.available_time_reg.tmpl` 中的錯誤內容
- [x] 確認作業表單模板正確引用

### 後端修改
- [x] 輔導項目作業 API (`counsel_work.go`)
- [x] 會員輔導作業 API (`counsel_work.go`)
- [x] 資料模型定義
- [ ] 輔導項目 API 調整（包含作業資料）

### 資料庫
- [x] 創建遷移檔案
- [x] 正確的欄位定義和關聯

### 整合測試
- [ ] 輔導項目設定頁面作業功能
- [ ] 學員預約時作業自動創建
- [ ] 前端作業顯示和上傳
- [ ] 後台作業管理

## 🚀 下一步行動

### 1. 立即修正
1. 恢復 `counsel.available_time_reg.tmpl` 到原始狀態
2. 確認 `counsel.type_reg.tmpl` 的修改正確

### 2. API 整合
1. 修改輔導項目 API 以包含作業資料
2. 測試前端與後端的整合

### 3. 功能測試
1. 測試輔導項目設定頁面的作業功能
2. 測試學員預約時的作業自動創建
3. 測試完整的作業生命週期

## 💡 重要提醒

### 資料一致性
- 確保 `counsel_works.counsel_type_id` 正確關聯到輔導項目
- 確保作業創建時使用正確的 ID

### 前端邏輯
- 作業設定在輔導項目頁面，不是輔導時間頁面
- 作業顯示在會員輔導頁面

### API 設計
- 輔導項目 API 應該包含作業資料
- 作業 API 應該正確處理輔導項目關聯

這個修正確保了輔導預約作業系統的邏輯正確性和資料一致性，讓作業功能在正確的位置實現。
