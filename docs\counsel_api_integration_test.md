# 輔導時間設定 API 整合測試指南

## 修改內容

已修正後端 API 的 `CreateCounselAvailableTime` 和 `UpdateCounselAvailableTime` 方法，使其能夠正確處理固定日期和常駐設定兩種不同類型的資料。

### 主要修改

#### 1. CreateCounselAvailableTime API
**修改前問題**：
- 只支援固定日期設定
- 強制要求 `date` 欄位
- 無法處理常駐設定的 `day_of_week` 欄位

**修改後解決方案**：
- 支援 `type` 欄位區分類型
- 根據類型驗證不同欄位
- 固定日期：驗證 `date` 欄位
- 常駐設定：驗證 `day_of_week` 欄位

#### 2. UpdateCounselAvailableTime API
**修改前問題**：
- 只能更新固定日期設定
- 強制要求 `date` 欄位
- 無法切換類型

**修改後解決方案**：
- 支援類型切換
- 根據類型更新相應欄位
- 智能重複檢查

#### 3. GetCounselAvailableTime API
**新增功能**：
- 添加星期名稱設定
- 支援兩種類型的資料回傳

## API 測試步驟

### 第一步：新增固定日期設定

#### 1.1 正確的請求格式
```bash
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "specific",
    "date": "2025-08-25",
    "time_slot_options": [
      {"start": "09:00", "end": "12:00"},
      {"start": "14:00", "end": "17:00"}
    ],
    "status": "Y",
    "note": "特殊活動日"
  }'
```

**預期回應**：
```json
{
  "msg": "建立成功",
  "id": 123
}
```

#### 1.2 錯誤情況測試
```bash
# 缺少日期
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "specific",
    "time_slot_options": [{"start": "09:00", "end": "12:00"}],
    "status": "Y"
  }'
```

**預期回應**：
```json
{
  "msg": "固定日期設定需要提供日期"
}
```

### 第二步：新增常駐設定

#### 2.1 正確的請求格式
```bash
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "recurring",
    "day_of_week": 0,
    "time_slot_options": [
      {"start": "10:00", "end": "16:00"}
    ],
    "status": "Y",
    "note": "星期日特殊時間"
  }'
```

**預期回應**：
```json
{
  "msg": "建立成功",
  "id": 124
}
```

#### 2.2 錯誤情況測試
```bash
# 缺少星期
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "recurring",
    "time_slot_options": [{"start": "10:00", "end": "16:00"}],
    "status": "Y"
  }'
```

**預期回應**：
```json
{
  "msg": "常駐設定需要提供星期"
}
```

### 第三步：更新設定測試

#### 3.1 更新固定日期設定
```bash
curl -X PATCH "/api/admin/counsel-available-times/123" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "specific",
    "date": "2025-08-26",
    "time_slot_options": [
      {"start": "08:00", "end": "18:00"}
    ],
    "status": "Y",
    "note": "調整後的活動日"
  }'
```

#### 3.2 更新常駐設定
```bash
curl -X PATCH "/api/admin/counsel-available-times/124" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "recurring",
    "day_of_week": 6,
    "time_slot_options": [
      {"start": "09:00", "end": "15:00"}
    ],
    "status": "Y",
    "note": "星期六調整時間"
  }'
```

#### 3.3 類型切換測試
```bash
# 將固定日期設定改為常駐設定
curl -X PATCH "/api/admin/counsel-available-times/123" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "recurring",
    "day_of_week": 1,
    "time_slot_options": [
      {"start": "09:00", "end": "17:00"}
    ],
    "status": "Y",
    "note": "改為星期一常駐設定"
  }'
```

### 第四步：查詢設定測試

#### 4.1 查詢單一設定
```bash
curl -X GET "/api/admin/counsel-available-times/123"
```

**預期回應（固定日期）**：
```json
{
  "data": {
    "id": 123,
    "type": "specific",
    "date": "2025-08-25",
    "day_of_week": null,
    "time_slot_options": [
      {"start": "09:00", "end": "12:00"},
      {"start": "14:00", "end": "17:00"}
    ],
    "status": "Y",
    "note": "特殊活動日",
    "day_of_week_name": ""
  }
}
```

**預期回應（常駐設定）**：
```json
{
  "data": {
    "id": 124,
    "type": "recurring",
    "date": null,
    "day_of_week": 0,
    "time_slot_options": [
      {"start": "10:00", "end": "16:00"}
    ],
    "status": "Y",
    "note": "星期日特殊時間",
    "day_of_week_name": "星期日"
  }
}
```

#### 4.2 查詢設定列表
```bash
# 查詢所有設定
curl -X GET "/api/admin/counsel-available-times"

# 篩選固定日期
curl -X GET "/api/admin/counsel-available-times?search[type]=specific"

# 篩選常駐設定
curl -X GET "/api/admin/counsel-available-times?search[type]=recurring"

# 篩選特定星期的常駐設定
curl -X GET "/api/admin/counsel-available-times?search[type]=recurring&search[day_of_week]=1"
```

### 第五步：重複檢查測試

#### 5.1 固定日期重複檢查
```bash
# 嘗試新增已存在的日期
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "specific",
    "date": "2025-08-25",
    "time_slot_options": [{"start": "09:00", "end": "12:00"}],
    "status": "Y"
  }'
```

**預期回應**：
```json
{
  "msg": "該日期已設定可預約時間，請選擇其他日期或編輯現有設定"
}
```

#### 5.2 常駐設定重複檢查
```bash
# 嘗試新增已存在的星期
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "recurring",
    "day_of_week": 1,
    "time_slot_options": [{"start": "09:00", "end": "12:00"}],
    "status": "Y"
  }'
```

**預期回應**：
```json
{
  "msg": "星期一已設定常駐可預約時間，請編輯現有設定"
}
```

### 第六步：錯誤處理測試

#### 6.1 無效類型
```bash
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "invalid",
    "date": "2025-08-25",
    "time_slot_options": [{"start": "09:00", "end": "12:00"}],
    "status": "Y"
  }'
```

**預期回應**：
```json
{
  "msg": "無效的類型，請選擇 specific 或 recurring"
}
```

#### 6.2 日期格式錯誤
```bash
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "specific",
    "date": "invalid-date",
    "time_slot_options": [{"start": "09:00", "end": "12:00"}],
    "status": "Y"
  }'
```

**預期回應**：
```json
{
  "msg": "日期格式錯誤"
}
```

## 驗證重點

### 1. API 請求格式
- [ ] 固定日期設定包含 `type: "specific"` 和 `date`
- [ ] 常駐設定包含 `type: "recurring"` 和 `day_of_week`
- [ ] 兩種類型都包含 `time_slot_options`

### 2. 驗證邏輯
- [ ] 固定日期設定不驗證 `day_of_week`
- [ ] 常駐設定不驗證 `date`
- [ ] 類型欄位必填
- [ ] 時間段格式正確

### 3. 重複檢查
- [ ] 固定日期按日期和類型檢查重複
- [ ] 常駐設定按星期和類型檢查重複
- [ ] 更新時排除自身 ID

### 4. 資料完整性
- [ ] 固定日期設定的 `day_of_week` 為 null
- [ ] 常駐設定的 `date` 為 null
- [ ] 類型欄位正確設定

### 5. 回應格式
- [ ] 成功回應包含正確的訊息和 ID
- [ ] 錯誤回應包含清楚的錯誤訊息
- [ ] 查詢回應包含完整的資料結構

## 成功指標

- [ ] 固定日期設定 API 正常運作
- [ ] 常駐設定 API 正常運作
- [ ] 類型切換功能正常
- [ ] 重複檢查邏輯正確
- [ ] 錯誤處理完善
- [ ] 前端整合無問題
- [ ] 資料庫約束正確執行

這些 API 修改確保了後端能夠正確處理兩種不同類型的時間設定，為前端提供了完整的支援。
