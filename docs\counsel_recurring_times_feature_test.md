# 輔導常駐可預約時間功能測試指南

## 功能概述

新增了輔導常駐可預約時間功能，允許管理員設定每週固定的可預約時間段，減少每週重複設定的工作量。

### 主要特色

1. **按星期設定**：可為每個星期（星期日到星期六）設定固定的可預約時間段
2. **優先級機制**：固定日期設定優先於常駐設定
3. **自動生成**：系統自動根據常駐設定生成未來30天的可預約日期
4. **重複檢查**：防止同一星期重複設定

### 優先級邏輯

```
固定日期設定（高優先級）> 常駐設定（低優先級）
```

- 如果某日期有固定設定且啟用，使用固定設定
- 如果某日期沒有固定設定，檢查該星期是否有常駐設定
- 如果都沒有，該日期不可預約

## 資料模型

### CounselRecurringTime 表結構
```sql
CREATE TABLE `counsel_recurring_times` (
    `id` INT(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    `day_of_week` TINYINT(1) NOT NULL,  -- 0=星期日, 1=星期一, ..., 6=星期六
    `time_slots` TEXT NULL,             -- JSON格式的時間段
    `status` ENUM('Y', 'N') DEFAULT 'Y', -- 啟用狀態
    `note` VARCHAR(500) NULL,           -- 備註
    -- 其他標準欄位...
);
```

### 預設資料
系統會自動插入工作日的預設設定：
- 星期一到星期五：09:00-12:00, 14:00-17:00
- 星期六：10:00-15:00
- 星期日：無設定

## 測試步驟

### 第一步：資料庫遷移測試

```bash
# 執行遷移
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" up

# 檢查新表格
mysql -u username -p database_name -e "DESCRIBE counsel_recurring_times;"

# 檢查預設資料
mysql -u username -p database_name -e "SELECT day_of_week, time_slots, status, note FROM counsel_recurring_times ORDER BY day_of_week;"
```

**預期結果**：
- 表格正確創建
- 包含星期一到星期六的預設設定
- 時間段為 JSON 格式

### 第二步：後台常駐設定管理測試

#### 2.1 常駐設定列表頁面
1. 訪問 `/admin/counsel/recurring-times`
2. **檢查項目**：
   - 顯示所有星期的設定
   - 星期名稱正確顯示（星期一、星期二...）
   - 時間段格式正確（如 "09:00-12:00, 14:00-17:00"）
   - 狀態顯示正確

#### 2.2 新增常駐設定
1. 點擊「新增常駐設定」
2. 選擇星期（如星期日）
3. 設定時間段：
   - 新增時間段：10:00-16:00
   - 測試時段重疊檢查
4. 設定狀態和備註
5. 提交表單

**預期結果**：
- 成功創建新的常駐設定
- 重複星期會被阻止
- 時段重疊檢查正常

#### 2.3 編輯常駐設定
1. 編輯現有的常駐設定
2. 修改時間段
3. 測試星期變更的重複檢查

#### 2.4 刪除常駐設定
1. 選擇要刪除的設定
2. 批量刪除測試

### 第三步：優先級邏輯測試

#### 3.1 固定日期優先測試
1. 設定星期一的常駐時間：09:00-17:00
2. 為特定星期一設定固定日期：10:00-15:00
3. 訪問前端預約頁面
4. **預期結果**：該特定星期一顯示 10:00-15:00（固定日期優先）

#### 3.2 常駐設定生效測試
1. 確保某個星期二沒有固定日期設定
2. 設定星期二的常駐時間：08:00-18:00
3. 檢查前端預約頁面
4. **預期結果**：該星期二顯示 08:00-18:00（常駐設定生效）

#### 3.3 無設定日期測試
1. 確保某個星期日既沒有固定設定也沒有常駐設定
2. 檢查前端預約頁面
3. **預期結果**：該星期日不出現在可預約日期中

### 第四步：前端整合測試

#### 4.1 可預約日期列表測試
1. 訪問 `/member/counsel`
2. 點擊「直接預約輔導」
3. **檢查項目**：
   - 日期下拉選單包含未來30天的可預約日期
   - 固定日期和常駐生成的日期都正確顯示
   - 日期備註正確（常駐設定會顯示 "常駐設定 - ..."）

#### 4.2 時間段顯示測試
1. 選擇一個由常駐設定生成的日期
2. **檢查項目**：
   - 時間段正確顯示
   - API 回應包含 `source: "recurring"`
3. 選擇一個固定日期設定的日期
4. **檢查項目**：
   - 時間段正確顯示
   - API 回應包含 `source: "specific"`

#### 4.3 預約提交測試
1. 選擇常駐設定生成的日期和時間
2. 完成預約流程
3. **預期結果**：預約成功創建

### 第五步：API 測試

#### 5.1 後台常駐設定 API
```bash
# 獲取常駐設定列表
curl -X GET "/api/admin/counsel-recurring-times"

# 新增常駐設定
curl -X POST "/api/admin/counsel-recurring-times" \
  -H "Content-Type: application/json" \
  -d '{
    "day_of_week": 0,
    "time_slot_options": [
      {"start": "10:00", "end": "16:00"}
    ],
    "status": "Y",
    "note": "星期日特殊時間"
  }'

# 更新常駐設定
curl -X PATCH "/api/admin/counsel-recurring-times/1" \
  -H "Content-Type: application/json" \
  -d '{
    "day_of_week": 1,
    "time_slot_options": [
      {"start": "08:00", "end": "18:00"}
    ],
    "status": "Y",
    "note": "星期一延長時間"
  }'
```

#### 5.2 前端可預約日期 API
```bash
# 獲取可預約日期（包含常駐生成的日期）
curl -X GET "/api/members/counsels/available-dates"

# 獲取指定日期的時間段（支援優先級邏輯）
curl -X GET "/api/members/counsels/available-times/2025-08-25"
```

**預期回應格式**：
```json
{
  "data": {
    "date": "2025-08-25",
    "time_slots": [
      {"start": "09:00", "end": "12:00"},
      {"start": "14:00", "end": "17:00"}
    ],
    "existing_appointments": [],
    "source": "recurring"
  }
}
```

### 第六步：邊界情況測試

#### 6.1 星期重複檢查
1. 嘗試為已設定的星期新增常駐設定
2. **預期結果**：顯示錯誤「星期X已設定常駐可預約時間，請編輯現有設定」

#### 6.2 時段重疊檢查
1. 在常駐設定中新增重疊的時間段
2. **預期結果**：顯示重疊警告，阻止提交

#### 6.3 狀態切換測試
1. 將常駐設定狀態改為停用
2. 檢查前端是否不再顯示該星期的日期
3. 重新啟用後檢查是否恢復顯示

#### 6.4 跨月份測試
1. 檢查常駐設定是否正確生成跨月份的日期
2. 確認月底和月初的日期都正確處理

## 效能考量

### 1. 日期生成效能
- 只生成未來30天，避免過多計算
- 使用記憶體操作，不涉及大量資料庫查詢

### 2. 優先級查詢效能
- 先查固定日期（索引查詢）
- 再查常駐設定（簡單條件查詢）
- 避免複雜的 JOIN 操作

### 3. 快取建議
- 可考慮快取常駐設定（變更頻率低）
- 可考慮快取生成的日期列表（每日更新）

## 管理建議

### 1. 設定策略
- 先設定常駐時間作為基礎
- 針對特殊日期設定固定時間覆蓋
- 定期檢查和調整常駐設定

### 2. 維護流程
- 每週檢查下週的可預約時間
- 節假日前設定特殊時間
- 定期清理過期的固定日期設定

### 3. 使用者教育
- 向管理員說明優先級邏輯
- 提供設定最佳實踐指南
- 建立常見問題解答

## 成功指標

- [ ] 常駐設定管理功能正常
- [ ] 優先級邏輯正確運作
- [ ] 前端正確顯示生成的日期
- [ ] 預約功能與新系統整合良好
- [ ] 重複檢查和驗證正常
- [ ] API 回應格式正確
- [ ] 效能表現良好
- [ ] 管理員工作量明顯減少

這個功能大幅提升了輔導預約系統的管理效率，讓管理員可以設定一次常駐時間，系統自動處理日常的可預約時間生成。
