package routes

import (
	. "cx/app/controllers/front"
	"cx/app/middleware"
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"

	. "cx/app/controllers/api"
	. "cx/app/controllers/api/front_api"
)

var frontendUserCount int32

func InitFrontRouter() *gin.Engine {
	router := gin.Default()
	router.LoadHTMLGlob("templates/front/**/*")
	router.Static("/assets", "./assets/front")
	router.Static("/common", "./assets/common")
	router.Static("/upload", "./assets/upload")
	router.Static("/uploads", "./uploads")
	router.Static("/checksum", "./checksum")

	store := cookie.NewStore([]byte("Taiwan is No.1!!!!"))
	router.Use(sessions.Sessions("my-session", store))
	router.Use(middleware.UserCountMiddleware(&frontendUserCount))

	api := router.Group("/api")

	router.NoRoute(func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/")
	})

	frontRouters(router.Group("/"))
	frontApiRouters(api.Group("/"))

	return router
}

func frontRouters(router *gin.RouterGroup) {
	router.GET("/", HomePage)
	about := router.Group("/about")
	about.GET("/", AboutPage).
		GET("/apply", AboutApplyPage).
		GET("/grade", AboutGradePage).
		GET("/learning", AboutLearningPage)
	router.GET("/qa", QuestionPage)
	router.GET("/contact", ContactPage)

	course := router.Group("/course")
	course.GET("/", CoursePage)

	product := router.Group("/product")
	product.GET("/:id", ProductPage).
		GET("/show/:id", ProductShowPage)

	goods := router.Group("/goods")
	goods.GET("/index", GoodsPage).
		GET("/show/:id", GoodsShowPage)

	counseling := router.Group("/counseling")
	counseling.GET("/index", CounselingPage).
		GET("/show/:id", CounselingShowPage)

	stored := router.Group("/stored")
	stored.Use(middleware.RequireMemberLogin("web", "/stored")).
		GET("", middleware.RequireAccountActive(), StoredPage).
		GET("/payment", StoredPaymentPage).
		GET("/remind", StoredRemindPage).
		GET("/checkout/:id", middleware.RequireAccountActive(), CheckoutPage).
		GET("/order", StoredOrderPage)

	member := router.Group("/member")
	// register
	member.GET("/terms", TermsPage).
		GET("/register", RegisterPage).
		GET("/register_ok", RegisterOkPage)
	// member index
	member.GET("/signin", MemberSignInPage).
		GET("/signout", MemberSignOutPage).
		GET("/forget", MemberForgetPage).
		GET("/reset-password", MemberResetPasswordPage).
		GET("/recert", MemberReCertPage).
		Use(middleware.RequireMemberLogin("web", "/member/index")).
		// member index
		GET("/index", MemberIndexPage).
		GET("/reg", MemberRegPage).
		GET("/order", MemberOrderPage).
		GET("product", MemberProductPage).
		GET("/work", MemberWorkPage).
		GET("goods", MemberGoodsPage).
		GET("/counsel", MemberCounselPage).
		GET("/coupon", MemberCouponPage).
		GET("/challenge", MemberChallengePage).
		// member verify
		Use(middleware.CheckMemberIsActive).
		GET("/verify/info", MemberVerifyInfoPage).
		GET("/verify/official", MemberVerifyOfficialPage).
		GET("/verify/real", MemberVerifyRealPage).
		GET("/verify/quiz", MemberVerifyQuizPage).
		GET("/verify/report", MemberVerifyReportPage)

	// 社群積分 (social)
	credit := router.Group("/credit")
	{
		credit.GET("/index", CreditIndexPage)
		credit.GET("/show/:id", CreditShowPage)
	}
}

func frontApiRouters(api *gin.RouterGroup) {
	checksum := api.Group("/checksum")
	checksum.GET("/", GetChecksum)

	api.Use(middleware.ErrorLogger())

	api.POST("/log-error", LogClientError)

	api.POST("/newbPay/order/notify", NotifyStoredOrder)
	api.POST("/ecpay/order/notify", NotifyEcpayOrder)

	member := api.Group("/members")
	// register
	member.POST("/register", RegisterMember)
	// login
	member.POST("/signin", SignInMember).
		GET("/forget", ForgetPassword).
		POST("/reset-password", ResetPasswordWithTemp).
		GET("/recert", ResendCertifyCode).
		Use(middleware.RequireMemberLogin("api", "/member/index")).
		GET("/credit", GetMemberSocialPoints).
		GET("/credits/logs", GetMemberSocialPointsLogs).
		// member index
		GET("/", GetMember).
		PATCH("/", UpdateMember).
		GET("/points", GetMemberPoints).
		GET("/orders", GetMemberOrders).
		GET("/calendar", GetMemberCalendar).
		PATCH("/booking", BookingCalendar).
		GET("/goods", GetMemberGoodsList).
		GET("/goods/records", GetMemberGoodsRecords).
		GET("/goods/expired", GetMemberExpiredGoods).
		POST("/goods", UseGoods).
		GET("/goods/pro_list", GetUsedProList).
		POST("/products/resume", ResumeProduct).
		Use(middleware.ResumeStopProductsByMemberID).
		GET("/products", GetMemberProducts).
		POST("/watch", UpdateMemberWatch)

	// challenge
	challenge := api.Group("/challenges")
	{
		challenge.Use(middleware.RequireMemberLogin("api", "/member/index"))
		challenge.GET("/progresses", GetChallengeProgresses)
		challenge.GET("/tasks/available", GetAvailableChallenges)
		challenge.GET("/tasks/pending", GetPendingTasks)
		challenge.GET("/tasks/completed", GetCompletedTasks)
		challenge.GET("/tasks/logs", GetTaskLogs)
		challenge.GET("/rewards", GetChallengeRewards)
		challenge.GET("/rewards/logs", GetChallengeRewardLogs)
		challenge.POST("/rewards/:id/receive", ReceiveReward)
	}

	// 級別申請
	membership := api.Group("/memberships")
	{
		membership.Use(middleware.RequireMemberLogin("api", "/member/index"))
		membership.GET("/", GetMembershipApplications)
		membership.GET("/l5-limit", GetMembershipApplicationL5Limit)
		membership.POST("/", CreateMembershipApplication)
		membership.POST("/upload", UploadMembershipExamImage)
	}

	// 年度學習計畫
	studyPlan := api.Group("/study-plans")
	{
		studyPlan.Use(middleware.RequireMemberLogin("api", "/member/index"))
		studyPlan.GET("/", GetStudyPlans)
		studyPlan.POST("/", CreateStudyPlan)
		studyPlan.POST("/retry", RetryStudyPlan)
	}

	counsel := member.Group("/counsels")
	counsel.GET("/avail", GetAvailCounselProducts).
		GET("/types", GetCounselTypes).
		GET("/available-dates", GetCounselAvailableDates).
		GET("/available-times/:date", GetAvailableTimesForDate).
		GET("/ready-to-schedule", GetReadyToScheduleAppointments).
		GET("/available-times", GetAvailableTimesForSchedule).
		POST("/schedule", ScheduleAppointmentTime).
		POST("/direct", ApplyCounselDirect).
		POST("/:mem_pro_id", ApplyCounsel).
		GET("/:mem_pro_id/goods", GetCounselGoods).
		GET("/appointments", GetCounselAppointments).
		POST("/confirm/:appoint_id", ConfirmCounsel).
		POST("/check/:appoint_id", CheckCounsel)

	counseling := api.Group("/counseling")
	counseling.GET("", GetCounselingList).
		GET("/:id", GetCounseling).
		Use(middleware.RequireMemberLogin("api", "/member/index")).
		POST("/buy/:id", BuyCounseling)

	// 會員輔導作業路由
	counselWorks := member.Group("/counsel-works")
	counselWorks.GET("/", GetMemberCounselWorks).
		POST("/:id/upload", UploadMemberCounselWork)

	verify := member.Group("/verify")
	verify.Use(middleware.RequireMemberLogin("api", "/member/index")).
		GET("/", GetMemberVerify).
		GET("/info", GetMemberVerifyInfo).
		POST("/info", UpdateMemberVerifyInfo).
		GET("/official", GetMemberVerifyOfficial).
		POST("/official", UpdateMemberVerifyOfficial).
		GET("/real", GetMemberVerifyReal).
		POST("/real", StartMemberVerifyReal).
		PATCH("/real", UpdateMemberVerifyReal).
		GET("/quiz", GetMemberVerifyQuiz).
		POST("/quiz", UploadVerifyQuizImg).
		POST("/report", UpdateMemberVerifyReport).
		Use(middleware.HandleOverdueReport).
		GET("/report", GetMemberVerifyReport).
		GET("/reports", GetMemberVerifyReports)

	work := member.Group("/works")
	work.GET("", GetMemberWorks).
		GET("/", GetMemberWorks).
		POST("", UploadMemberWork)

	// new work
	api.GET("/v2/members/works", GetMemberWorks)

	product := api.Group("/products")
	product.Use(middleware.DownExpiredProducts).
		GET("/kinds", GetProductKinds).
		GET("kinds/:id", GetProductKind).
		GET("/:id", GetProduct).
		GET("/", GetProductList).
		GET("/kinds/path", GetProductKindPath).
		Use(middleware.RequireMemberLogin("api", "/product/show/:id")).
		GET("/vimeo/:id", GetVimeoVideo).
		POST("/buy/:id", BuyProduct)

	goods := api.Group("/goods")
	goods.GET("/", GetGoodsList).
		GET("/:id", GetGoods).
		Use(middleware.RequireMemberLogin("api", "/goods/show/:id")).
		POST("/buy/:id", BuyGoods)

	credit := api.Group("/credits")
	{
		credit.GET("/", GetExchangeList)
		credit.GET("/:pro_id", GetExchangeProduct)
		credit.POST("/exchange/:pro_id", ExchangeProductHandler)
	}

	stored := api.Group("/stored")
	stored.Use(middleware.RequireMemberLogin("api", "/stored")).
		Use(middleware.RequireAccountActive()).
		GET("/", GetStoredList).
		GET("/:id", GetStored).
		GET("/payment_type", GetStoredPaymentType).
		GET("/order/:order_no", GetStoredOrder).
		POST("/pay_result/:order_no", UpdateOrderPayResult).
		GET("/unpaid", GetUnpaidOrderList).
		POST("/:order_no/cancel", CancelStored).
		POST("/checkout", CheckoutStored)

	coupon := api.Group("/coupons")
	coupon.Use(middleware.RequireMemberLogin("api", "/stored")).
		GET("/", GetCoupons).
		POST("/transfer", TransferCoupon)
}
