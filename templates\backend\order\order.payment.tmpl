<!DOCTYPE html>
<html>
    <head>
        {{template "head" .}}
        {{template "validate" .}}
        {{template "sweet_alert" .}}
        {{template "moment" .}}
        {{template "treeview" .}}
    </head>
    <body>
        {{template "top" .}}

        {{template "admin/order_payment" .}}

        {{template "bottom" .}}
    </body>

    <script>
        const app = createApp({
            delimiters: ['${', '}'],
            mixins: [CommonMixin, TreeViewMixin],
            data() {
                return {
                    show: false,
                    t_name: '',
                    // data
                    items: [],
                    // item
                    item: {
                        id: 0,
                        name: '',
                        payment_method: '', // atm, newebpay, zingala, hfc, ecpay
                        is_simulate: false,
                        bank_code: '',
                        account: '',
                        merchant_id: '',
                        hash_key: '',
                        hash_iv: '',
                        payment_type: 'CREDIT',
                        note: '',
                        status: 'Y',
                    },

                    isRange: false,
                }
            },
            mounted() {
                this.getData()
            },
            methods: {
                getData() {
                    axiosRequest()
                        .get('/api/admin/orders/payment_options')
                        .then(res => {
                            this.items = res.data.data

                            let data = []

                            this.items.forEach((item, index) => {
                                data.push({
                                    id: item.id,
                                    parent: "#",
                                    text: `${item.name} ${item.status==='N' ? '(已停用)' : ''}`,
                                    sorting: item.sorting,
                                    icon: this.ReorderIcon,
                                })
                            })

                            this.destroyTree("#reorder-tree")
                            this.renderReorderTree("#reorder-tree", data)
                        })
                        .catch(e => {
                            msgError(e.response.data.msg)
                            console.log(e.response.data.error)
                        })
                },
                getPayment() {
                    axiosRequest()
                        .get('/api/admin/orders/payment_options/' + this.item.id)
                        .then(res => {
                            this.bindJSON(this.item, res.data.data)
                            this.t_name = this.item.name
                        })
                },
                submitData() {
                    $('#main_form').validate()

                    if (!$('#main_form').valid())
                        return

                    if (this.item.id) this.updateData()
                    else this.createData()
                },
                createData() {
                    msgLoading()

                    axiosRequest('json')
                        .post('/api/admin/orders/payment_options/', this.item)
                        .then(res => {
                            this.item.id = res.data.data
                            this.getData()
                            this.showUpdate(this.item.id)

                            msgTopSuccess(res.data.msg)
                        })
                        .catch(e => {
                            console.log(e)
                            msgError(e.response.data.msg)
                        })
                },
                updateData() {
                    msgLoading()

                    axiosRequest('json')
                        .patch('/api/admin/orders/payment_options/' + this.item.id, this.item)
                        .then(res => {
                            this.getData()
                            this.showUpdate(this.item.id)

                            msgTopSuccess(res.data.msg)
                        })
                        .catch(e => {
                            msgError(e.response.data.msg)
                            console.log(e.response.data.error)
                        })
                },
                deleteData() {
                    msgConfirm(`確定要刪除<font color=red>【${ this.t_name }】</font>的付款方式嗎?`, 
                        () => {
                            axiosRequest()
                                .delete('/api/admin/orders/payment_options/' + this.item.id)
                                .then(res => {
                                    this.getData()
                                    this.show = false

                                    msgTopSuccess(res.data.msg)
                                })
                                .catch(e => {
                                    msgError(e.response.data.msg)
                                    console.log(e.response.data.error)
                                })
                        })
                },
                // show
                showCreate() {
                    this.show = true
                    this.isRange = false
                    this.resetData()
                },
                showUpdate(id) {
                    this.show = true
                    this.isRange = false
                    this.resetData()

                    this.item.id = id
                    this.getPayment()
                },
                // reset
                resetData() {
                    this.item = {
                        id: 0,
                        name: '',
                        payment_method: '', // atm, newebpay, zingala, hfc, ecpay
                        is_simulate: false,
                        bank_code: '',
                        account: '',
                        merchant_id: '',
                        hash_key: '',
                        hash_iv: '',
                        payment_type: 'CREDIT',
                        note: '',
                        status: 'Y',
                    }
                },
                showRange() {
                    this.show = false;
                    this.isRange = true;
                },
                async updateSorting(newOrder) {
                    msgLoading()
                    
                    axiosRequest('json')
                        .patch("/api/admin/orders/payment_options/sorting", newOrder)
                        .then(async res => {
                            await this.getData()
                            this.getDataByID()

                            msgTopSuccess("更新成功")
                        })
                        .catch(err => {
                            console.log(err)
                            msgError(err.response.data.msg)
                        })
                }
            }
        }).mount('#app')
    </script>
</html>

{{define "admin/order_payment"}}
    <div id="app" class="content_box">
        <div class="row" v-show="!isRange">
            <div class="col-sm-4">
                <div class="card">
                    <div class="card-header">
                        <button type="button" @click="showCreate" class="btn btn-light me-3">
                            <span class="fas fa-solid fa-plus"></span>
                            新增付款方式
                        </button>
                        <button type="button" @click="showRange" class="btn btn-light">排序</button>
                    </div>
                    <div class="card-header">選擇付款項目</div>
                    <div class="card-body">
                        <ul>
                            <li v-for="item in items">
                                <a href="javascript:void(0);" @click="showUpdate(item.id)" :class="{'text-danger': (item.status=='N')}">
                                    <span v-html="item.name"></span>
                                    <span v-if="item.status == 'N'">(已停用)</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-sm-8">
                <form id="main_form" @submit.prevent="submitData" class="card" v-show="show">
                    <div class="card-header">
                        <div v-if="item.id">
                            <span>
                                修改
                                <span class="text-danger">【${ t_name }】</span>
                                的付款方式
                            </span>
                        </div>
                        <div v-else>
                            新增付款方式
                        </div>
                    </div>
                    <div class="card-header">以下 * 欄位為必填欄位</div>

                    <div class="card-body">
                        <div class="row mb-3">
                            <label class="col-sm-3 col-md-2 col-form-label text-md-end">*名稱</label>
                            <div class="col-sm-9 col-md-10">
                                <input type="text" name="name" v-model="item.name" maxlength="100" class="form-control" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <label class="col-sm-3 col-md-2 col-form-label text-md-end">*付款方式</label>
                            <div class="col-sm-9 col-md-10">
                                <select name="payment_method" v-model="item.payment_method" class="form-select" required>
                                    <option value="" selected>請選擇</option>
                                    <option value="atm">ATM轉帳</option>
                                    <option value="newebpay">藍新金流</option>
                                    <option value="ecpay">綠界金流</option>
                                    <option value="zingala">中租貸款</option>
                                    <option value="hfc">和潤貸款</option>
                                </select>
                            </div>
                        </div>

                        {{template "payment.atm" .}}

                        {{template "payment.newebpay" .}}

                        {{template "payment.ecpay" .}}

                        <div class="row mb-3">
                            <label class="col-sm-3 col-md-2 col-form-label text-md-end">備註</label>
                            <div class="col-sm-9 col-md-10">
                                <textarea name="note" v-model="item.note" class="form-control" rows="3" maxlength="1000"></textarea>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-sm-3 col-md-2 col-form-label text-md-end">*狀態</label>
                            <div class="col-sm-9 col-md-10 d-flex align-items-center">
                                <div class="form-check form-check-inline">
                                    <label class=form-check-label>
                                        <input type="radio" name="status" value="Y" v-model="item.status" class="form-check-input">
                                        啟用
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <label class="form-check-label">
                                        <input type="radio" name="status" value="N" v-model="item.status" class="form-check-input">
                                        停用
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-sm-3 col-md-2 col-form-label"></label>
                            <div class="col-sm-9 col-md-10">
                                <template v-if="item.id">
                                    <button type="submit" class="btn btn-success me-3">修改</button>
                                    <button type="button" @click="deleteData" class="btn btn-light">刪除</button>
                                </template>
                                <template v-else>
                                    <button type="submit" class="btn btn-success">送出</button>
                                </template>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div v-show="isRange">
            {{template "range" .}}
        </div>
    </div>
{{end}}

{{define "payment.atm"}}
    <template v-if="item.payment_method == 'atm'">
        <div class="row mb-3">
            <label class="col-sm-3 col-md-2 col-form-label text-md-end">*收款銀行代碼</label>
            <div class="col-sm-9 col-md-10">
                <input type="text" name="atm_bank_code" v-model="item.bank_code" maxlength="10" class="form-control" required>
            </div>
        </div>
        <div class="row mb-3">
            <label class="col-sm-3 col-md-2 col-form-label text-md-end">*收款帳號</label>
            <div class="col-sm-9 col-md-10">
                <input type="text" name="atm_account" v-model="item.account" maxlength="20" class="form-control" required>
            </div>
        </div>
    </template>
{{end}}

{{define "payment.newebpay"}}
    <template v-if="item.payment_method == 'newebpay'">
        <div class="row mb-3">
            <label class="col-sm-3 col-md-2 col-form-label text-md-end">*模擬金流</label>
            <div class="col-sm-9 col-md-10 align-self-center">
                <div class="form-check form-check-inline">
                    <label class="form-check-label">
                        <input type="radio" name="simRadioOptions" v-model="item.is_simulate" :value="true" class="form-check-input" required>
                        是
                    </label>
                </div>
                <div class="form-check form-check-inline">
                    <label class="form-check-label">
                        <input type="radio" name="simRadioOptions" v-model="item.is_simulate" :value="false" class="form-check-input">
                        否
                    </label>
                </div>
            </div>
        </div>

        {{template "epay_keys" .}}
    </template>
{{end}}

{{define "payment.ecpay"}}
    <template v-if="item.payment_method == 'ecpay'">
        <div class="row mb-3">
            <label class="col-sm-3 col-md-2 col-form-label text-md-end">*模擬金流</label>
            <div class="col-sm-9 col-md-10 align-self-center">
                <div class="form-check form-check-inline">
                    <label class="form-check-label">
                        <input type="radio" name="simRadioOptions" v-model="item.is_simulate" :value="true" class="form-check-input" required>
                        是
                    </label>
                </div>
                <div class="form-check form-check-inline">
                    <label class="form-check-label">
                        <input type="radio" name="simRadioOptions" v-model="item.is_simulate" :value="false" class="form-check-input">
                        否
                    </label>
                </div>
            </div>
        </div>

        {{template "epay_keys" .}}
    </template>
{{end}}

{{define "epay_keys"}}
<template v-if="!item.is_simulate">
    <div class="row mb-3">
        <label class="col-sm-3 col-md-2 col-form-label text-md-end">*商店代號</label>
        <div class="col-sm-9 col-md-10">
            <input type="text" name="newebpay_merchant_id" v-model="item.merchant_id" maxlength="20" class="form-control" required>
        </div>
    </div>
    <div class="row mb-3">
        <label class="col-sm-3 col-md-2 col-form-label text-md-end">*HashKey</label>
        <div class="col-sm-9 col-md-10">
            <input type="text" name="newebpay_hash_key" v-model="item.hash_key" maxlength="50" class="form-control" required>
        </div>
    </div>
    <div class="row mb-3">
        <label class="col-sm-3 col-md-2 col-form-label text-md-end">*HashIV</label>
        <div class="col-sm-9 col-md-10">
            <input type="text" name="newebpay_hash_iv" v-model="item.hash_iv" maxlength="50" class="form-control" required>
        </div>
    </div>
    <div class="row mb-3">
        <label class="col-sm-3 col-md-2 col-form-label text-md-end">*支付方式</label>
        <div class="col-sm-9 col-md-10">
            <select name="payment_type" v-model="item.payment_type" class="form-select" required>
                <option value="" disabled>請選擇</option>
                <option value="CREDIT" selected>信用卡付款</option>
            </select>
        </div>
    </div>
</template>
{{end}}