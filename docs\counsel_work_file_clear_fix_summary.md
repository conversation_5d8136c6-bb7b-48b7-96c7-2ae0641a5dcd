# 輔導作業檔案清空問題修正總結

## 🐛 問題描述

在輔導項目設定頁面的作業表單中，當第二次以後打開編輯表單時，作業的檔案會被清空，導致使用者無法看到現有檔案資訊。

## 🔍 問題分析

### 問題根源
在 `showWorkForm` 方法中，當編輯現有作業時：

```javascript
// 原始有問題的程式碼
if (workItem) {
    this.workItem = {...workItem}  // 淺拷貝，可能丟失檔案資訊
    this._workItem = {...workItem}

    if (this.workItem.file) {  // 這裡的 file 是前端上傳物件，不是後端檔案路徑
        const trans = new DataTransfer()
        trans.items.add(this.workItem.file)
        $('#work_form [name=file]')[0].files = trans.files
    }
}
```

### 問題分析
1. **資料混淆**：`workItem.file` 是前端上傳的 File 物件，而 `workItem.work_file` 是後端儲存的檔案路徑
2. **狀態丟失**：每次重新打開表單時，前端的 File 物件會被清空
3. **邏輯錯誤**：試圖將不存在的 File 物件設定到檔案 input 中

### 資料結構說明
```javascript
// 後端資料結構
workItem = {
    id: 1,
    work_title: "作業標題",
    work_file: "/uploads/counsel_work_123.pdf",  // 後端檔案路徑
    // ... 其他欄位
}

// 前端上傳狀態
workItem = {
    // ... 後端資料
    file: File,        // 前端上傳的檔案物件
    del_file: false    // 是否刪除現有檔案
}
```

## ✅ 修正方案

### 修正後的 showWorkForm 邏輯
```javascript
if (workItem) {
    // 複製作業資料，但不包含 file 物件（file 物件是前端上傳用的）
    this.workItem = {
        ...this._defaultWorkItem,
        ...workItem,
        file: null,        // 重置 file 物件
        del_file: false    // 重置刪除檔案標記
    }
    this._workItem = {...this.workItem}
    
    // 設定 counsel_type_id
    this.workItem.counsel_type_id = this.data.id

    // 清空檔案 input（因為我們不保留上傳的檔案物件）
    $('#work_form [name=file]').val('')

    this.setDatePicker('#work_form [name=deadline_date]', this.workItem.deadline_date)
    this.setTimePicker(this.deadlineTime, this.workItem.deadline_date)
}
```

### 修正的關鍵點

#### 1. 分離檔案狀態
- **`work_file`**：後端儲存的檔案路徑，用於顯示現有檔案
- **`file`**：前端上傳的檔案物件，用於新上傳檔案
- **`del_file`**：標記是否要刪除現有檔案

#### 2. 正確的初始化
```javascript
this.workItem = {
    ...this._defaultWorkItem,  // 預設值
    ...workItem,               // 後端資料
    file: null,                // 重置上傳檔案
    del_file: false           // 重置刪除標記
}
```

#### 3. 檔案顯示邏輯
```html
<!-- 顯示現有檔案 -->
<div class="mt-2" v-if="workItem.work_file">
    <div class="d-flex align-items-center">
        <span class="me-2">目前檔案：</span>
        <a :href="workItem.work_file" target="_blank" class="btn btn-sm btn-outline-primary me-2">
            <i class="fas fa-download"></i> 查看檔案
        </a>
        <div class="form-check">
            <input class="form-check-input" type="checkbox" v-model="workItem.del_file" id="del_file">
            <label class="form-check-label text-danger" for="del_file">
                刪除現有檔案
            </label>
        </div>
    </div>
</div>
```

## 🔧 檔案處理流程

### 1. 編輯現有作業
```
1. 點擊編輯作業
2. showWorkForm 載入作業資料
3. work_file 顯示現有檔案連結
4. file 設為 null（沒有新上傳檔案）
5. del_file 設為 false（不刪除現有檔案）
```

### 2. 上傳新檔案
```
1. 選擇新檔案
2. onUploadWorkFile 設定 workItem.file
3. 提交時上傳新檔案
4. 後端更新 work_file 路徑
```

### 3. 刪除現有檔案
```
1. 勾選「刪除現有檔案」
2. del_file 設為 true
3. 提交時後端清空 work_file
```

## 🎯 使用者體驗改善

### 修正前的問題
- ❌ 第二次打開表單時看不到現有檔案
- ❌ 檔案狀態混亂
- ❌ 無法正確管理檔案

### 修正後的效果
- ✅ 每次打開表單都能看到現有檔案
- ✅ 檔案狀態清楚分離
- ✅ 可以正確上傳新檔案或刪除現有檔案
- ✅ 檔案操作邏輯清晰

## 📋 測試場景

### 1. 編輯有檔案的作業
1. 創建一個有檔案的作業
2. 關閉表單後重新打開編輯
3. **預期結果**：能看到現有檔案連結

### 2. 上傳新檔案
1. 編輯現有作業
2. 選擇新檔案上傳
3. 提交表單
4. **預期結果**：檔案成功更新

### 3. 刪除現有檔案
1. 編輯有檔案的作業
2. 勾選「刪除現有檔案」
3. 提交表單
4. **預期結果**：檔案被刪除

### 4. 多次編輯
1. 編輯作業多次
2. 每次都關閉後重新打開
3. **預期結果**：檔案狀態始終正確

## 🚀 技術改善

### 1. 資料結構清晰
- 明確分離前端上傳狀態和後端檔案資訊
- 避免資料混淆和狀態丟失

### 2. 狀態管理
- 正確初始化檔案相關狀態
- 確保每次打開表單時狀態一致

### 3. 使用者介面
- 清楚顯示現有檔案資訊
- 提供明確的檔案操作選項

這個修正確保了輔導作業檔案管理功能的穩定性和可用性，解決了檔案狀態混亂的問題。
