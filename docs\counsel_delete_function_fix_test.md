# 輔導時間設定刪除功能修復測試指南

## 問題分析

原本的刪除功能出現「無法取得刪除清單」錯誤，經過分析發現以下問題：

### 主要問題

#### 1. 前端缺少必要的資料屬性
**問題**：`selectedItems` 沒有在 Vue data 中定義
**影響**：無法追蹤選中的項目，導致刪除時沒有資料

#### 2. 缺少選擇功能的相關方法
**問題**：缺少 `selectAll` 方法和 `isAllSelected` 計算屬性
**影響**：全選功能無法正常運作

#### 3. 後端 API 資料綁定問題
**問題**：使用 `ShouldBind` 而不是 `ShouldBindJSON`
**影響**：無法正確解析 JSON 格式的請求體

## 修復內容

### 1. 前端修復

#### 1.1 添加 selectedItems 資料屬性
```javascript
data() {
    return {
        items: [],
        selectedItems: [], // 新增：追蹤選中的項目
        search: { ... },
        dayNames: [...]
    }
}
```

#### 1.2 添加 isAllSelected 計算屬性
```javascript
computed: {
    isAllSelected() {
        return this.items.length > 0 && this.selectedItems.length === this.items.length
    }
}
```

#### 1.3 添加 selectAll 方法
```javascript
selectAll() {
    if (this.isAllSelected) {
        this.selectedItems = []
    } else {
        this.selectedItems = this.items.map(item => item.id)
    }
}
```

### 2. 後端修復

#### 2.1 修正資料綁定方法
```go
// 修改前
if err := c.ShouldBind(&data); err != nil {

// 修改後
if err := c.ShouldBindJSON(&data); err != nil {
```

## 測試步驟

### 第一步：基本選擇功能測試

#### 1.1 單項選擇測試
1. 訪問 `/admin/counsel/available-times`
2. 點擊任一項目前的複選框
3. **檢查**：
   - 複選框正確選中
   - `selectedItems` 陣列包含該項目的 ID
   - 瀏覽器控制台無錯誤

#### 1.2 多項選擇測試
1. 選擇多個項目
2. **檢查**：
   - 所有選中的複選框都正確顯示
   - `selectedItems` 陣列包含所有選中項目的 ID

#### 1.3 全選功能測試
1. 點擊表格標題的全選複選框
2. **檢查**：
   - 所有項目都被選中
   - 全選複選框顯示為選中狀態
3. 再次點擊全選複選框
4. **檢查**：
   - 所有項目都被取消選中
   - 全選複選框顯示為未選中狀態

### 第二步：刪除功能測試

#### 2.1 未選擇項目時的刪除測試
1. 確保沒有選中任何項目
2. 點擊「刪除選取」按鈕
3. **預期結果**：
   - 顯示「請選擇要刪除的項目」錯誤訊息
   - 不會發送刪除請求

#### 2.2 單項刪除測試
1. 選擇一個項目
2. 點擊「刪除選取」按鈕
3. 在確認對話框中點擊確認
4. **預期結果**：
   - 顯示確認對話框
   - 發送正確的刪除請求
   - 顯示「刪除成功」訊息
   - 項目從列表中移除
   - `selectedItems` 被清空

#### 2.3 多項刪除測試
1. 選擇多個項目（如 3-5 個）
2. 點擊「刪除選取」按鈕
3. 確認刪除
4. **預期結果**：
   - 所有選中的項目都被刪除
   - 列表正確更新
   - 顯示成功訊息

#### 2.4 全選刪除測試
1. 使用全選功能選中所有項目
2. 點擊「刪除選取」按鈕
3. 確認刪除
4. **預期結果**：
   - 所有項目都被刪除
   - 列表顯示「暫無資料」

### 第三步：API 請求格式驗證

#### 3.1 檢查請求格式
使用瀏覽器開發者工具檢查刪除請求：

**請求方法**：DELETE
**請求 URL**：`/api/admin/counsel-available-times`
**請求標頭**：`Content-Type: application/json`
**請求體**：
```json
{
    "ids": [1, 2, 3]
}
```

#### 3.2 檢查回應格式
**成功回應**：
```json
{
    "msg": "刪除成功"
}
```

**錯誤回應**：
```json
{
    "msg": "無法取得刪除清單"
}
```

### 第四步：錯誤處理測試

#### 4.1 網路錯誤測試
1. 中斷網路連線
2. 嘗試刪除項目
3. **預期結果**：
   - 顯示網路錯誤訊息
   - 項目保持選中狀態

#### 4.2 伺服器錯誤測試
1. 選擇一個不存在的項目 ID（透過開發者工具修改）
2. 嘗試刪除
3. **預期結果**：
   - 顯示相應的錯誤訊息
   - 列表重新載入

#### 4.3 權限錯誤測試
1. 使用沒有刪除權限的帳號
2. 嘗試刪除項目
3. **預期結果**：
   - 顯示權限錯誤訊息

### 第五步：使用者體驗測試

#### 5.1 確認對話框測試
1. 選擇項目並點擊刪除
2. **檢查確認對話框**：
   - 訊息清楚明確
   - 有確認和取消按鈕
   - 點擊取消不會執行刪除

#### 5.2 視覺回饋測試
1. 執行刪除操作
2. **檢查視覺回饋**：
   - 載入指示器顯示
   - 成功訊息清楚可見
   - 列表平滑更新

#### 5.3 鍵盤操作測試
1. 使用 Tab 鍵導航到複選框
2. 使用空格鍵選擇/取消選擇
3. **檢查**：鍵盤操作正常運作

### 第六步：邊界情況測試

#### 6.1 大量項目選擇測試
1. 在有大量項目的情況下測試
2. 選擇大量項目（如 50+ 個）
3. 執行刪除操作
4. **檢查**：
   - 選擇操作流暢
   - 刪除操作成功
   - 效能表現良好

#### 6.2 快速操作測試
1. 快速連續選擇/取消選擇項目
2. 快速執行刪除操作
3. **檢查**：
   - 沒有競態條件
   - 狀態同步正確

## 除錯指南

### 1. 如果仍然出現「無法取得刪除清單」
**檢查項目**：
- 確認 `selectedItems` 在 Vue data 中正確定義
- 檢查複選框的 `v-model` 綁定
- 確認後端使用 `ShouldBindJSON`

### 2. 如果全選功能不正常
**檢查項目**：
- 確認 `isAllSelected` 計算屬性正確實現
- 檢查 `selectAll` 方法邏輯
- 確認全選複選框的 `:checked` 綁定

### 3. 如果刪除後列表沒有更新
**檢查項目**：
- 確認刪除成功後調用 `this.getData()`
- 檢查 `selectedItems` 是否被清空
- 確認 API 回應正確

## 成功指標

- [ ] `selectedItems` 正確追蹤選中項目
- [ ] 全選功能正常運作
- [ ] 單項和多項刪除都正常
- [ ] 不再出現「無法取得刪除清單」錯誤
- [ ] 確認對話框正確顯示
- [ ] 刪除後列表正確更新
- [ ] 錯誤處理機制正常
- [ ] 使用者體驗流暢
- [ ] API 請求格式正確
- [ ] 邊界情況處理良好

## 程式碼檢查清單

### 前端檢查
- [ ] `selectedItems: []` 在 data 中定義
- [ ] `isAllSelected` 計算屬性實現
- [ ] `selectAll` 方法實現
- [ ] 複選框正確綁定 `v-model="selectedItems"`
- [ ] 全選複選框正確綁定 `:checked="isAllSelected"`

### 後端檢查
- [ ] 使用 `ShouldBindJSON` 而不是 `ShouldBind`
- [ ] 正確處理 `ids` 陣列
- [ ] 適當的錯誤處理和回應

這個修復確保了刪除功能的完整性和可靠性，提供了良好的使用者體驗。
