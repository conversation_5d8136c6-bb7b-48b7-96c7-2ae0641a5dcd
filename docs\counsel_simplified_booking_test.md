# 輔導預約簡化流程測試指南

## 修改內容

已簡化直接預約輔導功能，移除複雜的時間選擇模態框，讓輔導項目點擊後直接進行預約申請，時間安排留待後續流程處理。

### 主要簡化

#### 1. 移除直接預約模態框
**修改前**：
- 需要選擇輔導項目
- 需要選擇預約日期
- 需要選擇輔導時長
- 需要選擇具體時間
- 複雜的時間計算邏輯

**修改後**：
- 只需選擇輔導項目
- 點擊後直接提交預約申請
- 時間安排留待後續處理

#### 2. 簡化表格顯示
**欄位調整**：
- 移除「可用時長」欄位
- 將「描述」改為「說明」
- 保持「輔導項目」和「申請」欄位
- 減少視覺複雜度

#### 3. 流程優化
**預約流程**：
1. 點擊「直接預約輔導」按鈕
2. 在表格中選擇輔導項目
3. 點擊「申請」按鈕
4. 直接提交預約申請
5. 顯示成功訊息

## UI 設計變更

### 1. 簡化的表格結構
```html
<table class="table table-responsive reflow-table reflow-table-w-50 reflow-table-sm">
    <thead>
        <tr>
            <th>輔導項目</th>
            <th>說明</th>
            <th>申請</th>
        </tr>
    </thead>
    <tbody>
        <!-- 課程輔導預約選項 -->
        <tr>
            <td>課程輔導預約</td>
            <td>針對已購買課程的專業輔導</td>
            <td><button @click="selectCourseBooking">申請</button></td>
        </tr>
        <!-- 其他輔導項目 -->
        <tr v-for="type in counselTypes">
            <td>${ type.name }</td>
            <td>${ type.description || '無說明' }</td>
            <td><button @click="submitDirectBooking(type)">申請</button></td>
        </tr>
    </tbody>
</table>
```

### 2. 簡化的 JavaScript 邏輯
```javascript
// 直接提交預約申請
submitDirectBooking(counselType) {
    const bookingData = {
        counsel_type_id: counselType.id,
        requested_duration: counselType.default_duration || 60,
        mem_pro_id: 0
    };

    axiosRequest()
    .post("/api/members/counsels/direct", bookingData)
        .then(res => {
            msgTopSuccess("預約申請成功，後續將安排時間");
            $.fancybox.close();
            this.getData();
        });
}
```

### 3. 移除的複雜功能
**不再需要的方法**：
- `resetDirectBookingForm()`
- `getAvailableDates()`
- `onDateChange()`
- `getAvailableTimesForDate()`
- `isTimeSlotAvailable()`
- `addMinutesToTime()`
- `generateAvailableTimeOptions()`
- `onCounselTypeChange()`

**不再需要的資料屬性**：
- `availableDates`
- `availableTimesForDate`
- `showDirectBooking`
- `directBookingForm`
- `selectedCounselType`

## 測試步驟

### 第一步：介面載入測試

#### 1.1 頁面訪問
1. 訪問 `/member/counsel`
2. 檢查頁面正常載入
3. 確認兩個預約按鈕都正確顯示

**預期結果**：
- 「直接預約輔導」按鈕（藍色）
- 「課程輔導預約」按鈕（金色）
- 按鈕樣式與原設計一致

#### 1.2 選項表格顯示
1. 點擊「直接預約輔導」按鈕
2. 檢查 Fancybox 彈出視窗
3. 確認表格正確顯示

**預期結果**：
- Fancybox 正常彈出
- 表格只有 3 欄：輔導項目、說明、申請
- 包含「課程輔導預約」選項
- 顯示所有可用的輔導項目
- 說明欄位顯示正確內容

### 第二步：課程輔導預約測試

#### 2.1 選擇課程輔導預約
1. 在選項表格中點擊「課程輔導預約」的申請按鈕
2. 檢查是否正確跳轉到原有的課程輔導預約介面

**預期結果**：
- 關閉選項表格
- 打開原有的 `#avail-table` 介面
- 顯示已購買的課程列表
- 功能與原有流程完全一致

#### 2.2 課程輔導預約流程
1. 選擇一個已購買的課程
2. 填寫預約資訊
3. 提交預約

**預期結果**：
- 所有原有功能正常運作
- 預約成功後正確儲存
- `counsel_type_id` 為 NULL
- 關聯正確的課程 ID

### 第三步：直接預約輔導測試

#### 3.1 選擇輔導項目
1. 在選項表格中選擇一個輔導項目
2. 點擊對應的申請按鈕

**預期結果**：
- 立即顯示載入提示
- 發送預約請求到後端
- 不會打開任何模態框或額外表單

#### 3.2 直接預約流程
1. 點擊輔導項目的申請按鈕
2. 等待請求完成

**預期結果**：
- 顯示「預約申請成功，後續將安排時間」訊息
- 關閉選項表格
- 重新載入預約列表
- 新的預約記錄出現在列表中

#### 3.3 預約資料驗證
```sql
-- 檢查直接預約記錄
SELECT id, counsel_type_id, requested_duration, appointment_date, appointment_time
FROM counsel_appointments 
WHERE counsel_type_id IS NOT NULL
ORDER BY id DESC LIMIT 5;
```

**預期結果**：
- `counsel_type_id` 有正確的輔導項目 ID
- `requested_duration` 設定為輔導項目的預設時長
- `appointment_date` 和 `appointment_time` 為 NULL（待後續安排）

### 第四步：使用者體驗測試

#### 4.1 操作簡化驗證
1. 比較新舊流程的操作步驟
2. 測試操作的直觀性

**預期結果**：
- 操作步驟明顯減少
- 使用者不需要選擇複雜的時間選項
- 流程更加直觀

#### 4.2 回饋機制測試
1. 測試成功預約的回饋
2. 測試錯誤情況的處理

**預期結果**：
- 成功訊息清楚說明後續流程
- 錯誤訊息適當顯示
- 載入狀態正確顯示

#### 4.3 響應式設計測試
1. 在不同螢幕尺寸下測試
2. 檢查表格響應式行為

**預期結果**：
- 在手機上正常顯示
- 表格正確重排
- 按鈕大小適中

### 第五步：資料一致性測試

#### 5.1 預約記錄檢查
1. 提交多個不同類型的預約
2. 檢查資料庫記錄

**預期結果**：
- 課程輔導預約：`counsel_type_id` 為 NULL
- 直接預約輔導：`counsel_type_id` 有值
- 其他欄位正確設定

#### 5.2 後續流程相容性
1. 檢查後台管理介面是否正常顯示
2. 確認時間安排功能不受影響

**預期結果**：
- 後台可以正常管理兩種類型的預約
- 時間安排功能正常運作

### 第六步：效能測試

#### 6.1 載入速度
1. 測試頁面初始載入時間
2. 檢查 Fancybox 彈出速度
3. 確認預約提交回應時間

**預期結果**：
- 頁面載入更快（移除複雜邏輯）
- 預約提交回應迅速
- 無不必要的 API 請求

#### 6.2 記憶體使用
1. 檢查是否有記憶體洩漏
2. 確認移除的方法不再被調用

**預期結果**：
- 記憶體使用減少
- 無 JavaScript 錯誤
- 效能提升

### 第七步：錯誤處理測試

#### 7.1 網路錯誤
1. 中斷網路連線
2. 嘗試提交預約

**預期結果**：
- 顯示適當的錯誤訊息
- 不會出現 JavaScript 錯誤

#### 7.2 資料錯誤
1. 測試輔導項目資料異常情況
2. 檢查 API 回應錯誤處理

**預期結果**：
- 錯誤處理機制正常
- 使用者能理解問題所在

## 成功指標

- [ ] 選項表格簡化為 3 欄顯示
- [ ] 「課程輔導預約」選項正常運作
- [ ] 直接預約輔導一鍵申請成功
- [ ] 移除所有複雜的時間選擇邏輯
- [ ] 預約資料正確儲存到資料庫
- [ ] 成功訊息清楚說明後續流程
- [ ] 響應式設計正常運作
- [ ] 效能有所提升
- [ ] 錯誤處理機制完善
- [ ] 與現有系統完全相容

## 後端 API 相容性

### 1. 直接預約 API
**端點**：`POST /api/members/counsels/direct`

**請求資料**：
```json
{
    "counsel_type_id": 1,
    "requested_duration": 60,
    "mem_pro_id": 0
}
```

**注意事項**：
- 不再發送 `appointment_date` 和 `appointment_time`
- 後端需要能處理這些欄位為空的情況
- 時間安排留待後續流程處理

### 2. 資料庫記錄
**直接預約記錄特徵**：
```sql
counsel_type_id: 有值（輔導項目 ID）
requested_duration: 有值（預設時長）
appointment_date: NULL（待安排）
appointment_time: NULL（待安排）
mem_pro_id: 0 或 NULL（可選關聯課程）
```

## 優勢總結

### 1. 使用者體驗
- **操作簡化**：從 5 步減少到 2 步
- **決策減少**：不需要選擇複雜的時間選項
- **回饋清楚**：明確說明後續流程

### 2. 系統效能
- **程式碼簡化**：移除大量複雜邏輯
- **載入更快**：減少不必要的 API 請求
- **維護容易**：邏輯更清晰

### 3. 業務流程
- **彈性更大**：時間安排可以更靈活
- **管理方便**：後台可以統一安排時間
- **衝突減少**：避免前端時間計算錯誤

這個簡化設計大幅提升了使用者體驗，同時保持了系統的功能完整性和相容性。
