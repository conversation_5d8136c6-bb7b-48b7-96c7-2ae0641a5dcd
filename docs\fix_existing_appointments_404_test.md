# 修復 checkExistingAppointments 404 錯誤測試指南

## 錯誤描述

在編輯輔導可預約時間時，`checkExistingAppointments` 方法發生 404 錯誤，無法正確載入該日期已有的預約資訊。

## 問題根因

1. **API 路徑錯誤**: 原本調用不存在的前端 API 路徑 `/api/members/counsels/available-times/${date}`
2. **搜尋參數不匹配**: 後端搜尋邏輯無法同時處理新舊兩種預約格式
3. **資料格式處理**: 需要正確處理直接預約和課程預約兩種不同的資料結構

## 修復內容

### 1. 前端修復
**檔案**: `templates/backend/counsel/counsel.available_time_reg.tmpl`

#### 1.1 修改 API 調用路徑
```javascript
// 修復前 - 調用不存在的 API
axiosRequest().get(`/api/members/counsels/available-times/${this.data.date}`)

// 修復後 - 使用正確的後台 API
axiosRequest().get(`/api/admin/counsels`, {
    params: {
        search: { appointment_date: this.data.date },
        per_page: 100
    }
})
```

#### 1.2 優化資料處理邏輯
```javascript
// 處理兩種預約格式
this.existingAppointments = (res.data.data || []).map(appointment => {
    let appointmentTime = ''
    let duration = 60
    
    // 新的直接預約格式
    if (appointment.appointment_time) {
        appointmentTime = appointment.appointment_time
        duration = appointment.requested_duration || 60
    }
    // 舊的課程預約格式
    else if (appointment.start_at) {
        appointmentTime = this.formatTime(appointment.start_at)
        duration = appointment.duration || 60
    }
    
    return {
        appointment_time: appointmentTime,
        requested_duration: duration
    }
}).filter(appointment => appointment.appointment_time)
```

#### 1.3 新增時間格式化方法
```javascript
formatTime(dateTimeStr) {
    if (!dateTimeStr) return ''
    try {
        const date = new Date(dateTimeStr)
        return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    } catch (e) {
        return ''
    }
}
```

### 2. 後端修復
**檔案**: `app/controllers/api/backend_api/counsel.go`

#### 2.1 新增預約日期搜尋支援
```go
// 支援按預約日期搜尋（新的直接預約格式）
if search["appointment_date"] != "" {
    conn = conn.Where("(DATE(counsel_appointments.appointment_date) = ? OR DATE(counsel_appointments.start_at) = ?)", 
        search["appointment_date"], search["appointment_date"])
}
```

這個修改讓後端 API 能夠同時搜尋：
- `appointment_date` 欄位（新的直接預約）
- `start_at` 欄位（舊的課程預約）

## 測試步驟

### 第一步：基本功能測試

#### 1.1 無預約日期測試
1. 訪問 `/admin/counsel/available-times/reg`
2. 選擇一個沒有任何預約的日期
3. **預期結果**: 
   - 不出現 404 錯誤
   - 不顯示「已有預約」區域
   - 可以正常設定時間段

#### 1.2 有預約日期測試
1. 選擇一個已有預約的日期
2. **預期結果**:
   - 不出現 404 錯誤
   - 正確顯示「已有預約」區域
   - 列出該日期的所有預約時間

### 第二步：預約格式兼容性測試

#### 2.1 直接預約格式測試
1. 選擇有直接預約的日期
2. **檢查項目**:
   - 正確顯示 `appointment_time` 
   - 正確顯示 `requested_duration`
   - 時間格式為 "HH:MM"

#### 2.2 課程預約格式測試
1. 選擇有課程預約的日期
2. **檢查項目**:
   - 正確從 `start_at` 提取時間
   - 正確顯示 `duration`
   - 時間格式為 "HH:MM"

#### 2.3 混合格式測試
1. 選擇同時有直接預約和課程預約的日期
2. **檢查項目**:
   - 兩種格式的預約都正確顯示
   - 時間不重複
   - 排序正確

### 第三步：衝突檢測測試

#### 3.1 與現有預約衝突
1. 在有預約的日期設定時間段
2. 設定與現有預約重疊的時間段
3. **預期結果**:
   - 顯示「與現有預約重疊」警告
   - 警告文字為橙色
   - 警告圖標正確顯示

#### 3.2 時間段邊界測試
1. 設定與預約相鄰但不重疊的時間段
2. **預期結果**: 不顯示衝突警告

### 第四步：API 直接測試

#### 4.1 測試後台預約查詢 API
```bash
curl -X GET "/api/admin/counsels?search[appointment_date]=2025-08-19&per_page=100"
```

**預期回應**:
```json
{
  "data": [
    {
      "counsel_id": 123,
      "appointment_time": "14:00",
      "requested_duration": 60,
      "booking_type": "direct"
    }
  ],
  "page": {...}
}
```

#### 4.2 測試搜尋參數
```bash
# 測試有直接預約的日期
curl -X GET "/api/admin/counsels?search[appointment_date]=2025-08-19"

# 測試有課程預約的日期  
curl -X GET "/api/admin/counsels?search[appointment_date]=2025-08-20"
```

### 第五步：瀏覽器開發者工具檢查

#### 5.1 網路請求檢查
1. 開啟瀏覽器開發者工具
2. 切換到 Network 標籤
3. 選擇日期觸發 `checkExistingAppointments`
4. **檢查項目**:
   - 請求 URL 為 `/api/admin/counsels`
   - 請求參數包含正確的 `search[appointment_date]`
   - 回應狀態碼為 200（不是 404）

#### 5.2 回應資料檢查
**正確的回應格式**:
```json
{
  "data": [
    {
      "counsel_id": 123,
      "appointment_time": "14:00",
      "requested_duration": 60,
      "start_at": "2025-08-19T14:00:00Z",
      "duration": 60,
      "booking_type": "direct"
    }
  ]
}
```

## 常見問題排除

### 問題 1：仍然出現 404 錯誤
**檢查**:
- 確認後台路由 `/api/admin/counsels` 存在
- 檢查管理員權限是否正確
- 確認請求參數格式正確

### 問題 2：預約資料顯示不正確
**檢查**:
- 確認資料庫中預約記錄的格式
- 檢查 `formatTime` 方法是否正確處理時間
- 確認過濾邏輯是否正確

### 問題 3：衝突檢測不準確
**檢查**:
- 確認 `hasConflictWithAppointment` 方法邏輯
- 檢查時間比較邏輯
- 確認 `addMinutesToTime` 方法正確

## 成功指標

- [ ] 不再出現 404 錯誤
- [ ] 正確顯示該日期的所有預約
- [ ] 支援直接預約和課程預約兩種格式
- [ ] 衝突檢測功能正常
- [ ] 時間格式顯示正確
- [ ] 網路請求使用正確的 API 路徑
- [ ] 後端搜尋邏輯支援新的搜尋參數

## 技術說明

### API 路徑對應
- **後台預約查詢**: `/api/admin/counsels`
- **前端預約查詢**: `/api/members/counsels/*`
- **可預約時間管理**: `/api/admin/counsel-available-times`

### 預約資料格式
- **直接預約**: 使用 `appointment_date`, `appointment_time`, `requested_duration`
- **課程預約**: 使用 `start_at`, `duration`

### 搜尋參數
- `appointment_date`: 按預約日期搜尋（支援兩種格式）
- `start_at`: 按開始日期搜尋（舊格式）
- `end_at`: 按結束日期搜尋（舊格式）

這次修復確保了預約衝突檢測功能能夠正確載入和顯示該日期的所有預約資訊。
