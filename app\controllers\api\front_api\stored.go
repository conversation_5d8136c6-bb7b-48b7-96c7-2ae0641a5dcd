package front_api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/KazumiLine/newebpay-go"
	ecpay "github.com/Laysi/go-ecpay-sdk"
	"github.com/gin-gonic/gin"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"

	"cx/app/models"
	. "cx/app/models"
	"cx/app/services"
	. "cx/app/utils"
	. "cx/database"
)

const (
	TestMerchantID = "MS351459442"
	TestHashKey    = "02QQhXLyggaimAtkMcNKAwIgvn14lejT"
	TestHashIV     = "CZFjGfwOYDeY9WWP"
)

const (
	EcpayTestMerchantID = "3366242"
	EcpayTestHashKey    = "aHk46nU5rBAIS7kp"
	EcpayTestHashIV     = "hpOOGBqq2jracs3x"
)

type NewebpayRequest struct {
	MerchantID string `json:"MerchantID"`
	TradeInfo  string `json:"TradeInfo"`
	TradeSha   string `json:"TradeSha"`
	Version    string `json:"Version"`
	Url        string `json:"Url"`
}

type EcpayRequest struct {
	MerchantID      string `json:"MerchantID"`
	MerchantTradeNo string `json:"MerchantTradeNo"`
	TradeAmt        string `json:"TradeAmt"`
	TradeDesc       string `json:"TradeDesc"`
	ItemName        string `json:"ItemName"`
	ReturnURL       string `json:"ReturnURL"`
	OrderResultURL  string `json:"OrderResultURL"`
	PaymentType     string `json:"PaymentType"`
	ChoosePayment   string `json:"ChoosePayment"`
	EncryptType     string `json:"EncryptType"`
	CheckMacValue   string `json:"CheckMacValue"`
}

func GetStoredList(c *gin.Context) {
	request := []PointStored{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Where("(start_at IS NULL OR start_at <= NOW()) AND (end_at IS NULL OR end_at >= NOW())").
		Order("sorting ASC").
		Find(&request).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得點數資料",
			"error": err.Error(),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": request,
		"msg":  "取得點數資料成功",
	})
}

func GetStored(c *gin.Context) {
	stored := &PointStored{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.First(&stored, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得點數資料",
			"error": err.Error(),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stored,
		"msg":  "取得點數資料成功",
	})
}

func GetStoredPaymentType(c *gin.Context) {
	payments := []PaymentOption{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Select("id", "name", "payment_method").
		Where("status = 'Y'").
		Order("sorting ASC").
		Find(&payments).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得付款方式",
			"error": err.Error(),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": payments,
		"msg":  "取得付款方式成功",
	})
}

func CheckoutStored(c *gin.Context) {
	var newebpay, rtn string
	order := &Order{}
	stored := &PointStored{}

	if err := c.ShouldBind(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "錯誤的訂單資料",
			"error": err.Error(),
		})
		return
	}

	// get member data
	info := GetMemberInfo(c)
	if info.ID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "無法取得會員資料",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	order.MemberID = info.ID

	if err := conn.Select("id", "point", "price", "fee").First(&stored, order.StoredID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得點數資料",
			"error": err.Error(),
		})
		return
	}

	order.StoredID = null.IntFrom(int64(stored.ID))
	order.Points = int(stored.Point)
	order.PayPrice = stored.Price
	order.PayFee = stored.Fee

	// get order no
	var count int64

	today := time.Now().Format("20060102")

	if err := conn.Model(&Order{}).Unscoped().Select("COUNT(`id`) + 1 AS count").
		Where("DATE_FORMAT(`created_at`, '%Y%m%d') = ?", today).Find(&count).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得訂單編號",
			"error": err.Error(),
		})
		return
	}

	order.OrderNo = fmt.Sprintf("%s%s%04d", "AA", today[2:], count)

	// get pay options
	payOpt := &PaymentOption{}
	if err := conn.Where("status = 'Y'").First(&payOpt, order.PayOptID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得付款方式",
			"error": err.Error(),
		})
		return
	}

	order.PayKind = payOpt.Name
	order.PayMethod = payOpt.PaymentMethod
	order.PayStatus = "N"
	order.IsPay = "N"

	// check coupon
	if order.CouponID.Valid {
		var coupon Coupon
		if err := conn.Where("id = ?", order.CouponID.Int64).
			First(&coupon).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無效的折扣券",
				"error": err.Error(),
			})
			return
		}

		if valid := CheckCouponValid(info.ID, order.StoredID.Int64, &coupon); !valid.IsValid {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg": valid.Msg,
			})
			return
		} else {
			order.PayDiscount = valid.Discount

			if coupon.CouponType == "private" && coupon.Code != nil && *coupon.Code != "" && coupon.MemberID.Int64 != int64(info.ID) {
				// 進行轉讓
				if err := conn.Model(&Coupon{}).Where("id = ?", order.CouponID.Int64).
					Update("transferred_to_id", info.ID).Error; err != nil {
					conn.Rollback()
					c.JSON(http.StatusBadRequest, gin.H{
						"msg":   "交易失敗",
						"error": err.Error(),
					})
					return
				}
			}
		}
	} else {
		conn = conn.Omit("coupon_id")
	}

	order.PayAmount = order.PayPrice + order.PayFee - order.PayDiscount

	if err := conn.Create(&order).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法新增訂單",
			"error": err.Error(),
		})
		return
	}

	if order.CouponID.Valid {
		// if err := conn.Model(&Coupon{}).Where("id = ?", order.CouponID.Int64).
		// 	Update("remaining_usage", gorm.Expr("remaining_usage - 1")).Error; err != nil {
		// 	conn.Rollback()
		// 	c.JSON(http.StatusBadRequest, gin.H{
		// 		"msg":   "無法更新折扣券",
		// 		"error": err.Error(),
		// 	})
		// 	return
		// }

		if err := conn.Create(&CouponUsage{
			CouponID: uint(order.CouponID.Int64),
			OrderID:  order.ID,
			MemberID: info.ID,
		}).Error; err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無法新增折扣券使用紀錄",
				"error": err.Error(),
			})
			return
		}
	}

	if order.PayMethod == "newebpay" {
		if payOpt.IsSimulate {
			// 模擬
			newebpay = newebpayTest(order)
		} else {
			if payOpt.MerchantID == "" || payOpt.HashKey == "" || payOpt.HashIV == "" {
				conn.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"msg": "付款設定錯誤，請聯絡客服人員",
				})
				return
			}

			newebpay = getNewebpayForm(payOpt, order)
		}
	} else if order.PayMethod == "ecpay" {
		if payOpt.IsSimulate {
			// 綠界模擬
			newebpay = ecpayTest(order)
		} else {
			if payOpt.MerchantID == "" || payOpt.HashKey == "" || payOpt.HashIV == "" {
				conn.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"msg": "付款設定錯誤，請聯絡客服人員",
				})
				return
			}

			newebpay = getEcpayForm(payOpt, order)
		}
	} else {
		rtn = "/stored/order?order_no=" + order.OrderNo
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"data":     order.ID,
		"url":      rtn,
		"newebpay": newebpay,
		"msg":      "訂單新增成功",
	})
}

func newebpayTest(order *Order) string {
	store := newebpay.NewStore(TestMerchantID, TestHashKey, TestHashIV, true)
	trade := store.NewTradeRequest(
		order.OrderNo, order.PayAmount, "當前消費為金流模擬並不會扣款\n信用卡請輸入4000-2211-1111-1111，其餘欄位任意填寫。").
		SetTradeLimit(900).UseCreditCard()
	trade = setNewebpayReqUrl(order, trade)

	html, err := trade.GenerateHTML()
	if err != nil {
		panic(err)
	}

	return html
}

func getNewebpayForm(opt *PaymentOption, order *Order) string {
	store := newebpay.NewStore(opt.MerchantID, opt.HashKey, opt.HashIV, false)
	trade := store.NewTradeRequest(order.OrderNo, order.PayAmount, "商品資訊詳細請查看官網").
		SetTradeLimit(900).UseCreditCard()
	trade = setNewebpayReqUrl(order, trade)

	html, err := trade.GenerateHTML()
	if err != nil {
		panic(err)
	}

	return html
}

func setNewebpayReqUrl(order *Order, trade *newebpay.TradeRequest) *newebpay.TradeRequest {
	domain := "https://www.graceschool.com.tw"
	notifyURL := domain + "/api/newbPay/order/notify"
	returnURL := domain + "/stored/order?order_no=" + order.OrderNo

	trade.SetNotifyURL(notifyURL).SetReturnURL(returnURL)

	return trade
}

// 藍新金流通知
func NotifyStoredOrder(c *gin.Context) {
	opt := &PaymentOption{}
	merchantID := c.PostForm("MerchantID")

	conn := ConnectDB()
	defer CloseDB(conn)

	if merchantID == TestMerchantID {
		// 模擬
		opt.MerchantID = TestMerchantID
		opt.HashIV = TestHashIV
		opt.HashKey = TestHashKey
		opt.IsSimulate = true
	} else if err := conn.Select("is_simulate", "merchant_id", "hash_key", "hash_iv").
		Where("merchant_id = ?", c.PostForm("MerchantID")).
		First(&opt).Error; err != nil {
		// get newebpay settings
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得付款方式",
			"error": err.Error(),
		})
		return
	}

	store := newebpay.NewStore(opt.MerchantID, opt.HashKey, opt.HashIV, opt.IsSimulate)

	if trade, err := store.ParseTradeResponse(c.Request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法解析交易資料",
			"error": err.Error(),
		})
		return
	} else {
		tradeInfo := trade.TradeInfo
		result := tradeInfo.Result

		newebpayOrder := &NewebpayOrder{
			OrderNo:     result.MerchantOrderNo,
			Status:      tradeInfo.Status,
			Message:     tradeInfo.Message,
			MerchantID:  result.MerchantID,
			Amt:         result.Amt,
			TradeNo:     result.TradeNo,
			PaymentType: result.PaymentType,
			IP:          result.IP,
			EscrowBank:  result.EscrowBank,
		}

		newPayConn := ConnectDB()
		defer CloseDB(newPayConn)

		// 新增newebpay交易資料
		if err := newPayConn.Create(&newebpayOrder).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無法新增交易資料",
				"error": err.Error(),
			})
			return
		}

		if IsTradePaid(trade) {
			order := Order{}
			if err := conn.Where("order_no = ?", result.MerchantOrderNo).
				First(&order).Error; err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "無法取得訂單資料",
					"error": err.Error(),
				})
				return
			}

			conn = conn.Begin()

			// 更新會員點數
			point := Point{}
			if err := conn.Select("id", "member_id", "points").
				Where("member_id = ?", order.MemberID).First(&point).Error; err != nil {
				if err := conn.Create(&Point{
					MemberID:  order.MemberID,
					Points:    order.Points,
					ExpiredAt: null.TimeFrom(GetPointExpiredAt(time.Now())),
				}).Error; err != nil {
					conn.Rollback()

					c.JSON(http.StatusBadRequest, gin.H{
						"msg":   "無法新增會員點數",
						"error": err.Error(),
					})
					return
				}
			} else {
				if err := conn.Model(&point).
					Update("points", gorm.Expr("points + ?", order.Points)).Error; err != nil {
					conn.Rollback()

					c.JSON(http.StatusBadRequest, gin.H{
						"msg":   "無法更新會員點數",
						"error": err.Error(),
					})
					return
				}

				// 更新點數到期日
				if err := conn.Model(&point).
					Update("expired_at", GetPointExpiredAt(time.Now())).Error; err != nil {
					conn.Rollback()

					c.JSON(http.StatusBadRequest, gin.H{
						"msg":   "無法更新會員點數",
						"error": err.Error(),
					})
					return
				}
			}

			// 新增點數紀錄
			if err := conn.Create(&PointHistory{
				MemberID: order.MemberID,
				OrderID:  null.IntFrom(int64(order.ID)),
				Points:   order.Points,
				Reason:   "訂單編號:" + order.OrderNo,
			}).Error; err != nil {
				conn.Rollback()

				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "無法新增點數紀錄",
					"error": err.Error(),
				})
				return
			}

			// 更新訂單狀態
			if err := conn.Exec("UPDATE `orders` SET `pay_status` = 'Y', `is_pay` = 'Y' WHERE `order_no` = ?", result.MerchantOrderNo).Error; err != nil {
				conn.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "無法更新訂單資料",
					"error": err.Error(),
				})
				return
			}

			member := Member{}
			if err := conn.Find(&member, order.MemberID).Error; err == nil {
				// 確認活動返點
				CheckOrderActivityQualify(conn, &member, order.ID)
			}

			// 確認級別儲值任務
			taskService := services.NewChallengeService(conn)
			taskService.CheckMembershipStoredTask(c, &models.ChallengeMembershipStoredTaskPayload{
				MemberID:  order.MemberID,
				OrderDate: order.CreatedAt,
			})

			conn.Commit()
		} else {
			// Fail
			if err := conn.Model(&Order{}).Where("order_no = ?", result.MerchantOrderNo).
				Update("pay_status", "F").Error; err != nil {
				conn.Rollback()

				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "無法更新訂單資料",
					"error": err.Error(),
				})
				return
			}
		}
	}

	c.JSON(http.StatusOK, nil)
}

func UpdateOrderPayResult(c *gin.Context) {
	var filePath string
	orderNo := c.Param("order_no")

	if orderNo == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "訂單編號錯誤",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	if img, err := c.FormFile("img"); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "上傳失敗",
		})
		return
	} else {
		Mkdir(OrderPayImgPath)

		filePath = fmt.Sprintf("%s%s.jpg", OrderPayImgPath, orderNo)
		if err := c.SaveUploadedFile(img, "./"+filePath); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "上傳失敗",
				"error": err.Error(),
			})
			return
		}

		if err := conn.Model(&Order{}).Where("order_no = ?", orderNo).Update("pay_result_img", filePath).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":   "無法更新訂單資料",
				"error": err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"msg":  "上傳成功",
		"path": filePath,
	})
}

func GetUnpaidOrderList(c *gin.Context) {
	data := []struct {
		OrderNo   string `json:"order_no"`
		PayKind   string `json:"pay_kind"`
		Points    int    `json:"points"`
		PayAmount int    `json:"pay_amount"`
		CreatedAt string `json:"created_at"`
	}{}
	page := &PageOption{}

	c.ShouldBind(&page)

	conn := ConnectDB()
	defer CloseDB(conn)

	info := GetMemberInfo(c)

	// 僅取得一個月內未付款的訂單
	// 除了newebpay的付款方式
	conn = conn.Where("member_id = ?", info.ID).
		Where("pay_method != 'newebpay'").
		Where("pay_status = 'N'").
		Where("created_at >= ?", time.Now().AddDate(0, -1, 0))

	GetPageCounts(conn, Order{}, page)

	if err := conn.Model(&Order{}).Select("order_no", "pay_kind", "points", "pay_amount", "created_at").
		Find(&data).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得訂單資料",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": data,
		"page": page,
		"msg":  "取得訂單資料成功",
	})
}

func CancelStored(c *gin.Context) {
	orderNo := c.Param("order_no")

	if orderNo == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "訂單編號錯誤",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	order := &Order{}
	if err := conn.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得訂單資料",
			"error": err.Error(),
		})
		return
	}

	if err := conn.Model(&Order{}).Where("order_no = ?", orderNo).
		Updates(map[string]interface{}{
			"pay_status": "C",
		}).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法更新訂單資料",
			"error": err.Error(),
		})
		return
	}

	if order.CouponID.Valid {
		// if err := conn.Model(&Coupon{}).Where("id = ?", order.CouponID.Int64).
		// 	Update("remaining_usage", gorm.Expr("remaining_usage + 1")).Error; err != nil {
		// 	conn.Rollback()
		// 	c.JSON(http.StatusBadRequest, gin.H{
		// 		"msg":   "無法更新折扣券",
		// 		"error": err.Error(),
		// 	})
		// 	return
		// }

		if err := conn.Where("order_id = ?", order.ID).Delete(&CouponUsage{}).Error; err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無法更新折扣券紀錄",
				"error": err.Error(),
			})
			return
		}
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "訂單取消成功",
	})

}

func GetStoredOrder(c *gin.Context) {
	order := &MemOrder{}

	conn := ConnectDB()
	defer CloseDB(conn)

	info := GetMemberInfo(c)

	if err := conn.Model(&Order{}).
		Select("orders.id", "order_no", "orders.pay_kind", "orders.pay_method", "orders.created_at AS order_at",
			"orders.points", "pay_amount", "pay_discount", "pay_result_img",
			"members.name AS member_name", "return_url", "hfc_return_url", "bank_code", "account", "order_payment_options.note").
		Joins("LEFT JOIN `members` ON members.id = orders.member_id").
		Joins("LEFT JOIN `point_stored` ON point_stored.id = orders.stored_id").
		Joins("LEFT JOIN `order_payment_options` ON order_payment_options.id = orders.pay_opt_id").
		Where("order_no = ?", c.Param("order_no")).
		Where("member_id = ?", info.ID).
		First(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無訂單資料",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": order,
	})
}

// 綠界金流測試環境
func ecpayTest(order *Order) string {
	client := ecpay.NewStageClient(
		ecpay.WithReturnURL(getDomain() + "/api/ecpay/order/notify"),
	)

	html, err := client.CreateOrder(order.OrderNo, time.Now(), order.PayAmount, "當前消費為金流模擬並不會扣款", []string{"點數儲值"}).
		SetAllPayment().
		GenerateRequestHtml()

	if err != nil {
		panic(err)
	}

	return html
}

// 綠界金流正式環境
func getEcpayForm(opt *PaymentOption, order *Order) string {
	client := ecpay.NewClient(opt.MerchantID, opt.HashKey, opt.HashIV, getDomain()+"/api/ecpay/order/notify")

	html, err := client.CreateOrder(order.OrderNo, time.Now(), order.PayAmount, "點數儲值", []string{"點數儲值"}).
		SetAllPayment().
		GenerateRequestHtml()

	if err != nil {
		panic(err)
	}

	return html
}

// 取得網域名稱
func getDomain() string {
	return "https://www.graceschool.com.tw"
}

// 綠界金流通知處理
func NotifyEcpayOrder(c *gin.Context) {
	opt := &PaymentOption{}
	merchantID := c.PostForm("MerchantID")

	conn := ConnectDB()
	defer CloseDB(conn)

	if merchantID == EcpayTestMerchantID {
		// 模擬
		opt.MerchantID = EcpayTestMerchantID
		opt.HashIV = EcpayTestHashIV
		opt.HashKey = EcpayTestHashKey
		opt.IsSimulate = true
	} else if err := conn.Select("is_simulate", "merchant_id", "hash_key", "hash_iv").
		Where("merchant_id = ?", merchantID).
		First(&opt).Error; err != nil {
		// get ecpay settings
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得付款方式",
			"error": err.Error(),
		})
		return
	}

	var client *ecpay.Client
	if opt.IsSimulate {
		client = ecpay.NewStageClient(
			ecpay.WithReturnURL(getDomain() + "/api/ecpay/order/notify"),
		)
	} else {
		client = ecpay.NewClient(opt.MerchantID, opt.HashKey, opt.HashIV, getDomain()+"/api/ecpay/order/notify")
	}

	// 解析回傳參數
	rtnCode := c.PostForm("RtnCode")
	rtnMsg := c.PostForm("RtnMsg")
	merchantTradeNo := c.PostForm("MerchantTradeNo")
	tradeNo := c.PostForm("TradeNo")
	tradeAmt := c.PostForm("TradeAmt")
	paymentDate := c.PostForm("PaymentDate")
	paymentType := c.PostForm("PaymentType")
	paymentTypeChargeFee := c.PostForm("PaymentTypeChargeFee")
	tradeDate := c.PostForm("TradeDate")
	simulatePaid := c.PostForm("SimulatePaid")
	checkMacValue := c.PostForm("CheckMacValue")

	// 簡化驗證，實際應用中應該使用 SDK 提供的驗證方法
	// 這裡先跳過驗證，實際部署時需要實現正確的驗證邏輯

	// 轉換金額
	amt, err := strconv.Atoi(tradeAmt)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "金額格式錯誤",
			"error": err.Error(),
		})
		return
	}

	ecpayOrder := &EcpayOrder{
		OrderNo:              merchantTradeNo,
		RtnCode:              rtnCode,
		RtnMsg:               rtnMsg,
		MerchantID:           merchantID,
		MerchantTradeNo:      merchantTradeNo,
		TradeNo:              tradeNo,
		TradeAmt:             amt,
		PaymentDate:          paymentDate,
		PaymentType:          paymentType,
		PaymentTypeChargeFee: paymentTypeChargeFee,
		TradeDate:            tradeDate,
		SimulatePaid:         simulatePaid,
		CheckMacValue:        checkMacValue,
	}

	ecpayConn := ConnectDB()
	defer CloseDB(ecpayConn)

	// 新增綠界金流交易資料
	if err := ecpayConn.Create(&ecpayOrder).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法新增交易資料",
			"error": err.Error(),
		})
		return
	}

	// 檢查交易是否成功
	if IsEcpayTradePaid(rtnCode) {
		order := Order{}
		if err := conn.Where("order_no = ?", merchantTradeNo).
			First(&order).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無法取得訂單資料",
				"error": err.Error(),
			})
			return
		}

		conn = conn.Begin()

		// 更新會員點數
		point := Point{}
		if err := conn.Select("id", "member_id", "points").
			Where("member_id = ?", order.MemberID).First(&point).Error; err != nil {
			if err := conn.Create(&Point{
				MemberID:  order.MemberID,
				Points:    order.Points,
				ExpiredAt: null.TimeFrom(GetPointExpiredAt(time.Now())),
			}).Error; err != nil {
				conn.Rollback()

				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "無法新增會員點數",
					"error": err.Error(),
				})
				return
			}
		} else {
			if err := conn.Model(&point).
				Update("points", gorm.Expr("points + ?", order.Points)).Error; err != nil {
				conn.Rollback()

				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "無法更新會員點數",
					"error": err.Error(),
				})
				return
			}

			// 更新點數到期日
			if err := conn.Model(&point).
				Update("expired_at", GetPointExpiredAt(time.Now())).Error; err != nil {
				conn.Rollback()

				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "無法更新會員點數",
					"error": err.Error(),
				})
				return
			}
		}

		// 新增點數紀錄
		if err := conn.Create(&PointHistory{
			MemberID: order.MemberID,
			OrderID:  null.IntFrom(int64(order.ID)),
			Points:   order.Points,
			Reason:   "訂單編號:" + order.OrderNo,
		}).Error; err != nil {
			conn.Rollback()

			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無法新增點數紀錄",
				"error": err.Error(),
			})
			return
		}

		// 更新訂單狀態
		if err := conn.Exec("UPDATE `orders` SET `pay_status` = 'Y', `is_pay` = 'Y' WHERE `order_no` = ?", merchantTradeNo).Error; err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無法更新訂單資料",
				"error": err.Error(),
			})
			return
		}

		member := Member{}
		if err := conn.Find(&member, order.MemberID).Error; err == nil {
			// 確認活動返點
			CheckOrderActivityQualify(conn, &member, order.ID)
		}

		// 確認級別儲值任務
		taskService := services.NewChallengeService(conn)
		taskService.CheckMembershipStoredTask(c, &models.ChallengeMembershipStoredTaskPayload{
			MemberID:  order.MemberID,
			OrderDate: order.CreatedAt,
		})

		conn.Commit()
	} else {
		// Fail
		if err := conn.Model(&Order{}).Where("order_no = ?", merchantTradeNo).
			Update("pay_status", "F").Error; err != nil {
			conn.Rollback()

			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "無法更新訂單資料",
				"error": err.Error(),
			})
			return
		}
	}

	c.String(http.StatusOK, "1|OK")
}

// 判斷綠界金流交易是否成功
func IsEcpayTradePaid(rtnCode string) bool {
	return rtnCode == "1"
}
