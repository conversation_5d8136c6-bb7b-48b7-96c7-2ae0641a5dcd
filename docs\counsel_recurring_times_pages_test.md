# 輔導常駐時間設定頁面測試指南

## 新增的頁面

### 1. 常駐時間列表頁面
**路徑**: `/admin/counsel/recurring-times`
**檔案**: `templates/backend/counsel/counsel.recurring_times.tmpl`

### 2. 常駐時間編輯頁面
**路徑**: `/admin/counsel/recurring-times/reg` (新增)
**路徑**: `/admin/counsel/recurring-times/reg/{id}` (編輯)
**檔案**: `templates/backend/counsel/counsel.recurring_time_reg.tmpl`

### 3. 更新的現有頁面
- `counsel.available_times.tmpl` - 新增常駐設定按鈕和說明
- `counsel.available_time_reg.tmpl` - 新增說明區域

## 快速測試步驟

### 第一步：檢查頁面訪問

1. **常駐時間列表頁面**
   ```
   訪問: /admin/counsel/recurring-times
   檢查: 頁面正常載入，顯示預設的常駐設定
   ```

2. **常駐時間新增頁面**
   ```
   訪問: /admin/counsel/recurring-times/reg
   檢查: 表單正常顯示，星期下拉選單有7個選項
   ```

3. **從可預約時間頁面跳轉**
   ```
   訪問: /admin/counsel/available-times
   點擊: 「常駐設定」按鈕
   檢查: 正確跳轉到常駐時間列表頁面
   ```

### 第二步：功能測試

#### 2.1 常駐設定列表功能
1. 檢查預設資料是否正確顯示
2. 測試星期篩選功能
3. 測試狀態篩選功能
4. 檢查星期名稱顯示（星期一、星期二...）
5. 檢查時間段格式（09:00-12:00, 14:00-17:00）

#### 2.2 新增常駐設定
1. 選擇星期日（預設沒有設定）
2. 設定時間段：10:00-16:00
3. 測試時段重疊檢查
4. 提交並檢查是否成功

#### 2.3 編輯常駐設定
1. 編輯現有的星期一設定
2. 修改時間段
3. 測試星期變更的重複檢查
4. 提交並檢查更新

#### 2.4 重複檢查測試
1. 嘗試新增已存在星期的設定
2. 檢查錯誤訊息：「星期X已設定常駐可預約時間，請編輯現有設定」

### 第三步：介面檢查

#### 3.1 常駐時間列表頁面
- [ ] 標題顯示「輔導常駐可預約時間管理」
- [ ] 有「新增常駐設定」和「刪除選取」按鈕
- [ ] 說明區域清楚解釋常駐設定概念
- [ ] 表格欄位：星期、可預約時間段、狀態、備註、建立時間、操作
- [ ] 星期顯示為中文（星期一、星期二...）
- [ ] 時間段格式正確
- [ ] 分頁功能正常

#### 3.2 常駐時間編輯頁面
- [ ] 標題顯示「新增/編輯常駐可預約時間」
- [ ] 說明區域解釋常駐設定的概念和優先級
- [ ] 星期下拉選單有7個選項（星期日到星期六）
- [ ] 時間段可以新增/移除
- [ ] 時段重疊檢查和視覺提醒
- [ ] 狀態選擇（啟用/停用）
- [ ] 備註欄位
- [ ] 提交和取消按鈕

#### 3.3 更新的現有頁面
- [ ] 可預約時間列表頁面有「常駐設定」按鈕
- [ ] 兩個頁面都有說明區域解釋優先級關係
- [ ] 說明文字清楚易懂

### 第四步：資料驗證

#### 4.1 檢查預設資料
```sql
SELECT day_of_week, time_slots, status, note 
FROM counsel_recurring_times 
ORDER BY day_of_week;
```

**預期結果**：
- 星期一(1)到星期五(5)：09:00-12:00, 14:00-17:00
- 星期六(6)：10:00-15:00
- 所有狀態都是 'Y'

#### 4.2 檢查新增的資料
1. 新增星期日設定後檢查資料庫
2. 確認 JSON 格式正確
3. 確認唯一約束生效

### 第五步：整合測試

#### 5.1 前端預約頁面測試
1. 訪問 `/member/counsel`
2. 點擊「直接預約輔導」
3. 檢查日期下拉選單是否包含常駐設定生成的日期
4. 選擇一個常駐生成的日期，檢查時間段是否正確

#### 5.2 優先級測試
1. 為星期一設定固定日期（如明天是星期一）
2. 檢查前端是否使用固定日期設定而非常駐設定
3. 刪除固定日期設定，檢查是否回退到常駐設定

## 常見問題排除

### 問題 1：頁面 404 錯誤
**檢查**：
- 路由是否正確添加到 `routes/backend_routes.go`
- 控制器方法是否正確實現

### 問題 2：API 錯誤
**檢查**：
- API 路由是否正確添加
- 控制器方法是否正確實現
- 資料庫遷移是否執行

### 問題 3：前端 JavaScript 錯誤
**檢查**：
- 瀏覽器 Console 是否有錯誤
- API 回應格式是否正確
- Vue.js 語法是否正確

### 問題 4：時間段顯示異常
**檢查**：
- JSON 格式是否正確
- 解析邏輯是否正確
- 前端格式化方法是否正確

## 成功指標

- [ ] 所有頁面正常載入
- [ ] 常駐設定 CRUD 功能正常
- [ ] 重複檢查和驗證正常
- [ ] 時段重疊檢查正常
- [ ] 前端整合正常
- [ ] 優先級邏輯正確
- [ ] 說明文字清楚易懂
- [ ] 使用者體驗良好

## 下一步測試

完成基本功能測試後，可以進行：
1. 效能測試
2. 併發測試
3. 邊界情況測試
4. 使用者接受度測試

這些頁面提供了完整的常駐時間管理功能，讓管理員可以輕鬆設定和維護每週的固定可預約時間。
