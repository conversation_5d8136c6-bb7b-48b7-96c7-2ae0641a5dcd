# 輔導時間設定 Date 欄位 NULL 約束修復測試指南

## 問題分析

### 錯誤詳情
```
column 'date' cannot be null
```

### 根本原因
1. **資料庫約束問題**：原始表結構中 `date` 欄位被定義為 `NOT NULL`
2. **常駐設定需求**：常駐設定不需要 `date` 欄位，應該為 `NULL`
3. **遷移不完整**：遷移檔案沒有修改 `date` 欄位的 NULL 約束
4. **資料模型衝突**：兩種類型的設定需要不同的欄位組合

### 具體場景
當創建常駐設定時：
- `type` = "recurring"
- `day_of_week` = 1 (星期一)
- `date` = NULL (應該為空)

但資料庫約束要求 `date NOT NULL`，導致插入失敗。

## 修復方案

### 1. 資料庫結構修改
**修改 date 欄位約束**：
```sql
ALTER TABLE `counsel_available_times` 
MODIFY COLUMN `date` DATE NULL COMMENT '可預約日期（固定日期使用）';
```

### 2. 遷移檔案更新
**更新 000228 遷移**：
- 將 `date` 欄位改為可空
- 確保兩種類型的資料都能正確插入

### 3. 模型方法增強
**CreateOrUpdate 方法優化**：
```go
// 根據類型確保正確的欄位設定
if cat.Type == "specific" {
    // 固定日期設定：確保 day_of_week 為 NULL
    cat.DayOfWeek = nil
} else if cat.Type == "recurring" {
    // 常駐設定：確保 date 為 NULL
    cat.Date = nil
}
```

### 4. 資料完整性保證
**欄位互斥邏輯**：
- 固定日期設定：`date` 有值，`day_of_week` 為 NULL
- 常駐設定：`day_of_week` 有值，`date` 為 NULL

## 測試步驟

### 第一步：執行資料庫遷移

```bash
# 如果已經執行過 000228 遷移，需要先回滾
migrate -path database/migrations -database "mysql://connection" down 1

# 重新執行修復後的遷移
migrate -path database/migrations -database "mysql://connection" up

# 檢查 date 欄位是否可空
mysql -u username -p database_name -e "DESCRIBE counsel_available_times;"
```

**預期結果**：
- `date` 欄位的 `Null` 欄位顯示為 `YES`
- `type` 和 `day_of_week` 欄位正確添加

### 第二步：固定日期設定測試

#### 2.1 創建固定日期設定
1. 訪問 `/admin/counsel/available-times/reg`
2. 選擇類型：「固定日期」
3. 設定日期：2025-09-01
4. 設定時間段：09:00-17:00
5. 提交表單

**預期結果**：
- 創建成功
- 資料庫中 `date` 有值，`day_of_week` 為 NULL
- `type` 為 'specific'

#### 2.2 檢查資料庫記錄
```sql
SELECT id, date, type, day_of_week, time_slots 
FROM counsel_available_times 
WHERE type = 'specific' 
ORDER BY id DESC LIMIT 1;
```

**預期結果**：
```
| id | date       | type     | day_of_week | time_slots |
|----|------------|----------|-------------|------------|
| XX | 2025-09-01 | specific | NULL        | [...]      |
```

### 第三步：常駐設定測試

#### 3.1 創建常駐設定
1. 訪問 `/admin/counsel/available-times/reg`
2. 選擇類型：「常駐設定」
3. 選擇星期：星期日 (0)
4. 設定時間段：10:00-16:00
5. 提交表單

**預期結果**：
- 創建成功，不再出現 "column 'date' cannot be null" 錯誤
- 資料庫中 `day_of_week` 有值，`date` 為 NULL
- `type` 為 'recurring'

#### 3.2 檢查資料庫記錄
```sql
SELECT id, date, type, day_of_week, time_slots 
FROM counsel_available_times 
WHERE type = 'recurring' 
ORDER BY id DESC LIMIT 1;
```

**預期結果**：
```
| id | date | type      | day_of_week | time_slots |
|----|------|-----------|-------------|------------|
| XX | NULL | recurring | 0           | [...]      |
```

### 第四步：批量創建測試

#### 4.1 創建多個常駐設定
1. 創建星期一常駐設定
2. 創建星期二常駐設定
3. 創建星期三常駐設定

**預期結果**：
- 所有創建都成功
- 無 NULL 約束錯誤

#### 4.2 創建多個固定日期設定
1. 創建 2025-09-02 設定
2. 創建 2025-09-03 設定
3. 創建 2025-09-04 設定

**預期結果**：
- 所有創建都成功
- 日期欄位正確設定

### 第五步：編輯功能測試

#### 5.1 編輯固定日期設定
1. 編輯現有的固定日期設定
2. 修改日期和時間段
3. 保存更改

**預期結果**：
- 更新成功
- `date` 欄位正確更新
- `day_of_week` 保持為 NULL

#### 5.2 編輯常駐設定
1. 編輯現有的常駐設定
2. 修改星期和時間段
3. 保存更改

**預期結果**：
- 更新成功
- `day_of_week` 欄位正確更新
- `date` 保持為 NULL

#### 5.3 類型切換測試
1. 將固定日期設定改為常駐設定
2. 將常駐設定改為固定日期設定

**預期結果**：
- 類型切換成功
- 相應欄位正確設定/清空

### 第六步：資料一致性驗證

#### 6.1 檢查欄位互斥性
```sql
-- 檢查固定日期設定的資料完整性
SELECT COUNT(*) as invalid_specific
FROM counsel_available_times 
WHERE type = 'specific' 
AND (date IS NULL OR day_of_week IS NOT NULL);

-- 檢查常駐設定的資料完整性
SELECT COUNT(*) as invalid_recurring
FROM counsel_available_times 
WHERE type = 'recurring' 
AND (day_of_week IS NULL OR date IS NOT NULL);
```

**預期結果**：兩個查詢都應該返回 0

#### 6.2 檢查預設資料
```sql
-- 檢查遷移插入的預設常駐設定
SELECT day_of_week, date, type, status 
FROM counsel_available_times 
WHERE type = 'recurring' 
ORDER BY day_of_week;
```

**預期結果**：
- 星期一到星期六的常駐設定
- 所有 `date` 欄位都為 NULL
- 所有 `day_of_week` 欄位都有正確的值

### 第七步：前端整合測試

#### 7.1 列表頁面顯示
1. 訪問 `/admin/counsel/available-times`
2. 檢查兩種類型的設定都正確顯示

**預期結果**：
- 固定日期顯示具體日期
- 常駐設定顯示星期名稱
- 類型標籤正確顯示

#### 7.2 前端預約功能
1. 訪問前端預約頁面
2. 檢查可預約日期是否包含常駐設定生成的日期

**預期結果**：
- 常駐設定正確生成可預約日期
- 優先級邏輯正確（固定日期優先）

### 第八步：錯誤處理測試

#### 8.1 無效資料測試
1. 嘗試創建 `type='specific'` 但 `date=NULL` 的記錄
2. 嘗試創建 `type='recurring'` 但 `day_of_week=NULL` 的記錄

**預期結果**：
- API 層驗證阻止無效資料
- 返回適當的錯誤訊息

#### 8.2 約束測試
1. 嘗試創建重複的固定日期設定
2. 嘗試創建重複的常駐設定

**預期結果**：
- 重複檢查正常運作
- 返回重複錯誤訊息

## 監控要點

### 1. 錯誤監控
- 監控是否還有 "column 'date' cannot be null" 錯誤
- 檢查其他可能的 NULL 約束錯誤

### 2. 資料完整性監控
- 定期檢查欄位互斥性
- 驗證資料類型一致性

### 3. 功能完整性監控
- 確認兩種類型的設定都能正常創建
- 驗證前端整合功能正常

## 成功指標

- [ ] 常駐設定創建不再出現 NULL 約束錯誤
- [ ] 固定日期設定功能正常
- [ ] 資料庫 date 欄位可空
- [ ] 欄位互斥邏輯正確
- [ ] 預設資料正確插入
- [ ] 編輯功能正常
- [ ] 類型切換功能正常
- [ ] 前端整合無問題
- [ ] 資料一致性良好
- [ ] 遷移可正常回滾

## 長期維護建議

### 1. 資料驗證
在應用層添加更嚴格的資料驗證：
```go
// 驗證資料完整性
func (cat *CounselAvailableTime) Validate() error {
    if cat.Type == "specific" && cat.Date == nil {
        return errors.New("固定日期設定需要提供日期")
    }
    if cat.Type == "recurring" && cat.DayOfWeek == nil {
        return errors.New("常駐設定需要提供星期")
    }
    return nil
}
```

### 2. 資料庫檢查
定期執行資料完整性檢查，確保沒有無效的資料組合。

### 3. 文檔更新
更新相關文檔，說明兩種類型的資料結構差異。

這個修復徹底解決了常駐設定創建時的 NULL 約束問題，確保了兩種類型的設定都能正常運作。
