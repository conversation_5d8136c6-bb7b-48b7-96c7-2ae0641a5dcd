# 輔導預約系統 Duration 矩陣更新測試指南

## 更新概述

本次更新將 CounselType 的 duration 相關設定從範圍型（min/max）改為矩陣型（固定選項），並移除了 sorting 欄位。

## 主要變更

### 1. 資料庫結構變更
- **新增欄位**：`available_durations` (TEXT) - 存儲 JSON 格式的時長選項陣列
- **移除欄位**：`min_duration`, `max_duration`, `sorting`
- **保留欄位**：`default_duration` - 作為預設選項

### 2. 資料模型變更
- 新增 `AvailableDurations` 字串欄位存儲 JSON
- 新增 `DurationOptions` 虛擬欄位用於前端處理
- 新增 `ParseDurationOptions()` 方法解析 JSON
- 新增 `IsValidDuration()` 方法驗證時長

### 3. 前端介面變更
- **後台管理**：改為動態新增/移除時長選項的介面
- **會員預約**：輔導項目和時長都改為 select 下拉選擇

## 測試步驟

### 第一步：資料庫遷移測試
```bash
# 1. 備份資料庫
mysqldump -u username -p database_name > backup_before_duration_update.sql

# 2. 執行遷移
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" up

# 3. 驗證表格結構
mysql -u username -p database_name -e "DESCRIBE counsel_types;"

# 4. 檢查資料是否正確遷移
mysql -u username -p database_name -e "SELECT id, name, available_durations, default_duration FROM counsel_types;"
```

預期結果：
- `available_durations` 欄位存在且包含 JSON 格式資料
- `min_duration`, `max_duration`, `sorting` 欄位已移除
- 現有資料的 `available_durations` 應包含合理的時長選項

### 第二步：後台管理功能測試

#### 2.1 輔導項目列表頁面
1. 訪問 `/admin/counsel/types`
2. 檢查列表是否正常顯示
3. 確認不再顯示 min/max duration 和 sorting 欄位

#### 2.2 輔導項目編輯頁面
1. 訪問 `/admin/counsel/types/reg` (新增)
2. 測試新增時長選項功能：
   - 點擊「新增時長選項」按鈕
   - 輸入時長值（如 45）
   - 確認選項正確新增並排序
3. 測試移除時長選項功能：
   - 嘗試移除時長選項
   - 確認至少保留一個選項的限制
4. 測試預設時長選擇：
   - 確認預設時長下拉選單只顯示可用選項
   - 測試選擇不同的預設時長
5. 提交表單並檢查儲存是否成功

#### 2.3 編輯現有項目
1. 訪問 `/admin/counsel/types/reg/1` (編輯)
2. 確認現有資料正確載入
3. 修改時長選項並儲存
4. 重新載入頁面確認變更已保存

### 第三步：前端預約功能測試

#### 3.1 輔導項目選擇
1. 訪問 `/member/counsel`
2. 點擊「直接預約輔導」按鈕
3. 檢查輔導項目下拉選單：
   - 確認顯示為 select 而非 card
   - 確認選項格式：「項目名稱 - 描述」
4. 選擇不同的輔導項目

#### 3.2 時長選擇
1. 選擇輔導項目後
2. 檢查時長下拉選單：
   - 確認只顯示該項目的可用時長選項
   - 確認選項格式：「XX 分鐘」
3. 測試選擇不同時長
4. 測試切換輔導項目時時長選項的更新

#### 3.3 預約提交
1. 完整填寫預約表單
2. 提交預約
3. 確認預約成功創建
4. 檢查後台是否正確記錄選擇的時長

### 第四步：API 測試

#### 4.1 獲取輔導項目 API
```bash
curl -X GET "/api/members/counsels/types" \
  -H "Authorization: Bearer <token>"
```

預期回應：
```json
{
  "data": [
    {
      "id": 1,
      "name": "A輔導",
      "description": "基礎輔導項目",
      "available_durations": "[30,60,90]",
      "default_duration": 60,
      "duration_options": [30, 60, 90]
    }
  ]
}
```

#### 4.2 直接預約 API
```bash
curl -X POST "/api/members/counsels/direct" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "counsel_type_id": 1,
    "appointment_date": "2024-01-15",
    "appointment_time": "14:00",
    "requested_duration": 60
  }'
```

#### 4.3 後台輔導項目管理 API
```bash
# 獲取項目詳情
curl -X GET "/api/admin/counsel-types/1"

# 更新項目
curl -X PATCH "/api/admin/counsel-types/1" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "A輔導",
    "available_durations": "[15,30,60,90]",
    "default_duration": 30
  }'
```

## 驗證要點

### 1. 資料完整性
- [ ] 現有預約記錄不受影響
- [ ] 輔導項目資料正確遷移
- [ ] JSON 格式的時長選項正確解析

### 2. 功能正確性
- [ ] 後台可以管理時長選項
- [ ] 前端正確顯示可用選項
- [ ] 預約驗證邏輯正常運作

### 3. 使用者體驗
- [ ] 介面直觀易用
- [ ] 錯誤訊息清楚明確
- [ ] 操作流程順暢

### 4. 向後兼容性
- [ ] 舊的預約記錄正常顯示
- [ ] API 回應格式兼容

## 常見問題排除

### 問題 1：時長選項顯示為空
**原因**：`available_durations` 欄位為空或格式錯誤
**解決**：檢查資料庫資料，確保 JSON 格式正確

### 問題 2：預設時長不在可用選項中
**原因**：資料遷移時預設時長未包含在選項中
**解決**：更新 `available_durations` 包含預設時長

### 問題 3：前端時長選項不更新
**原因**：JavaScript 事件處理問題
**解決**：檢查 `onCounselTypeChange` 方法是否正確觸發

## 回滾程序

如果發現問題需要回滾：

```bash
# 回滾資料庫
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" down 1

# 恢復程式碼
git checkout <previous-commit>

# 重新部署
```

## 效能考量

1. **JSON 解析**：每次載入輔導項目時都會解析 JSON，對效能影響微小
2. **資料庫查詢**：查詢邏輯未改變，效能無影響
3. **前端渲染**：select 元素比 card 元素渲染更快

## 後續優化建議

1. **快速設定**：提供常用時長組合的快速設定按鈕
2. **批量操作**：支援批量修改多個輔導項目的時長設定
3. **統計分析**：分析最常選擇的時長，優化預設設定
