# 輔導時間設定專用狀態更新 API 測試指南

## 修改內容

已創建專門的狀態更新 API 端點，並修改前端代碼使用新的 API，提升效率並符合 RESTful 設計原則。

### 主要改進

#### 1. 新增專用狀態更新 API
**端點**: `PATCH /api/admin/counsel-available-times/{id}/status`

**功能特色**：
- 只更新狀態欄位
- 輕量級請求，提升效能
- 專門的驗證邏輯
- 符合單一職責原則

#### 2. 前端代碼優化
**修改前**：
- 使用完整更新 API
- 需要發送所有欄位資料
- 請求體較大

**修改後**：
- 使用專用狀態更新 API
- 只發送狀態資料
- 請求體最小化

#### 3. 錯誤處理改善
**狀態恢復機制**：
- 記錄原始狀態
- 失敗時直接恢復前端狀態
- 無需重新載入整個列表

## API 設計

### 1. 請求格式
```http
PATCH /api/admin/counsel-available-times/{id}/status
Content-Type: application/json

{
    "status": "Y"  // 或 "N"
}
```

### 2. 回應格式
**成功回應**：
```json
{
    "msg": "狀態更新成功"
}
```

**錯誤回應**：
```json
{
    "msg": "狀態值無效，請選擇 Y 或 N"
}
```

### 3. 驗證邏輯
- 檢查 ID 是否存在
- 驗證狀態值（只允許 "Y" 或 "N"）
- 更新 `updated_by_id` 和 `updated_at`

## 前端實現

### 1. 優化的 updateStatus 方法
```javascript
updateStatus(item) {
    // 記錄原始狀態，以便失敗時恢復
    const originalStatus = item.status

    // 準備狀態更新資料
    const statusData = {
        status: item.status
    }

    axiosRequest('json')
        .patch(`/api/admin/counsel-available-times/${item.id}/status`, statusData)
        .then(res => {
            msgTopSuccess('狀態更新成功')
        })
        .catch(err => {
            console.log(err);
            msgError(err.response.data.msg)
            // 如果更新失敗，恢復原狀態
            item.status = originalStatus
        })
}
```

### 2. 改進特點
- **輕量級請求**：只發送必要的狀態資料
- **快速恢復**：失敗時直接恢復狀態，無需重新載入
- **更好的使用者體驗**：減少網路流量和等待時間

## 測試步驟

### 第一步：API 端點測試

#### 1.1 正確的狀態更新
```bash
# 更新為啟用
curl -X PATCH "/api/admin/counsel-available-times/123/status" \
  -H "Content-Type: application/json" \
  -d '{"status": "Y"}'

# 更新為停用
curl -X PATCH "/api/admin/counsel-available-times/123/status" \
  -H "Content-Type: application/json" \
  -d '{"status": "N"}'
```

**預期回應**：
```json
{
  "msg": "狀態更新成功"
}
```

#### 1.2 錯誤情況測試
```bash
# 無效狀態值
curl -X PATCH "/api/admin/counsel-available-times/123/status" \
  -H "Content-Type: application/json" \
  -d '{"status": "X"}'
```

**預期回應**：
```json
{
  "msg": "狀態值無效，請選擇 Y 或 N"
}
```

```bash
# 不存在的 ID
curl -X PATCH "/api/admin/counsel-available-times/99999/status" \
  -H "Content-Type: application/json" \
  -d '{"status": "Y"}'
```

**預期回應**：
```json
{
  "msg": "查無資料"
}
```

#### 1.3 缺少必要欄位
```bash
# 缺少 status 欄位
curl -X PATCH "/api/admin/counsel-available-times/123/status" \
  -H "Content-Type: application/json" \
  -d '{}'
```

**預期回應**：
```json
{
  "msg": "錯誤資料"
}
```

### 第二步：前端整合測試

#### 2.1 狀態切換測試
1. 訪問 `/admin/counsel/available-times`
2. 找到一個啟用的設定
3. 點擊狀態下拉選單，選擇「停用」
4. **檢查網路請求**：
   - 請求 URL: `/api/admin/counsel-available-times/{id}/status`
   - 請求方法: PATCH
   - 請求體: `{"status": "N"}`
5. **預期結果**：
   - 顯示「狀態更新成功」
   - 下拉選單保持在「停用」狀態

#### 2.2 錯誤處理測試
1. 使用開發者工具模擬網路錯誤
2. 嘗試修改狀態
3. **預期結果**：
   - 顯示錯誤訊息
   - 狀態自動恢復到修改前的值
   - 無需重新載入頁面

#### 2.3 快速連續操作測試
1. 快速連續修改多個設定的狀態
2. **檢查**：
   - 每個請求都使用正確的 API 端點
   - 請求體只包含狀態資料
   - 回應速度較快

### 第三步：效能比較測試

#### 3.1 請求大小比較
**舊 API 請求體**（完整更新）：
```json
{
    "type": "specific",
    "date": "2025-08-25",
    "status": "N",
    "note": "特殊活動日",
    "time_slot_options": [
        {"start": "09:00", "end": "12:00"},
        {"start": "14:00", "end": "17:00"}
    ]
}
```

**新 API 請求體**（狀態更新）：
```json
{
    "status": "N"
}
```

**改善**：請求體大小減少約 80-90%

#### 3.2 回應時間測試
1. 使用瀏覽器開發者工具測量
2. 比較新舊 API 的回應時間
3. **預期**：新 API 回應時間更快

### 第四步：資料一致性測試

#### 4.1 資料庫更新驗證
1. 修改設定狀態
2. 直接查詢資料庫確認：
   ```sql
   SELECT id, status, updated_by_id, updated_at 
   FROM counsel_available_times 
   WHERE id = {測試ID};
   ```
3. **檢查**：
   - 狀態正確更新
   - `updated_by_id` 設定為當前管理員 ID
   - `updated_at` 更新為當前時間

#### 4.2 其他欄位保持不變
1. 記錄修改前的所有欄位值
2. 執行狀態更新
3. 確認除了 `status`、`updated_by_id`、`updated_at` 外，其他欄位保持不變

### 第五步：併發測試

#### 5.1 多使用者同時修改
1. 開啟多個瀏覽器視窗
2. 同時修改同一個設定的狀態
3. **檢查**：
   - 最後的修改生效
   - 沒有資料衝突
   - 前端狀態正確同步

#### 5.2 大量請求測試
1. 快速連續發送多個狀態更新請求
2. **檢查**：
   - 所有請求都正確處理
   - 沒有請求丟失
   - 伺服器穩定運行

### 第六步：安全性測試

#### 6.1 權限檢查
1. 使用沒有權限的帳號嘗試更新
2. **預期**：返回權限錯誤

#### 6.2 參數驗證
1. 嘗試發送惡意參數
2. **預期**：正確的驗證和錯誤處理

## API 優勢

### 1. 效能提升
- **請求體更小**：只包含必要的狀態資料
- **處理更快**：只更新單一欄位
- **網路流量減少**：減少不必要的資料傳輸

### 2. 設計更好
- **單一職責**：專門處理狀態更新
- **RESTful 設計**：符合資源操作的最佳實踐
- **可維護性**：邏輯清晰，易於維護

### 3. 使用者體驗
- **回應更快**：減少等待時間
- **錯誤恢復更好**：失敗時直接恢復狀態
- **操作更流暢**：減少頁面重新載入

### 4. 可擴展性
- **易於擴展**：可以為其他欄位創建類似的專用 API
- **版本控制**：不影響現有的完整更新 API
- **向後相容**：保持現有功能不變

## 成功指標

- [ ] 新的狀態更新 API 正常運作
- [ ] 前端使用新 API 進行狀態更新
- [ ] 請求體大小明顯減少
- [ ] 回應時間有所改善
- [ ] 錯誤處理機制正常
- [ ] 資料一致性良好
- [ ] 併發操作穩定
- [ ] 安全性檢查通過

## 後續優化建議

### 1. 其他欄位的專用 API
可以考慮為其他常用欄位創建類似的專用更新 API：
- `PATCH /{id}/note` - 更新備註
- `PATCH /{id}/time-slots` - 更新時間段

### 2. 批量狀態更新
可以創建批量狀態更新 API：
- `PATCH /status` - 批量更新多個設定的狀態

### 3. 即時通知
可以添加 WebSocket 或 Server-Sent Events 來即時通知其他使用者狀態變更。

這個專用的狀態更新 API 大幅提升了系統的效能和使用者體驗，同時保持了良好的程式碼結構和可維護性。
