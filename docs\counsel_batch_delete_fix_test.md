# 輔導時間設定批量刪除約束衝突修復測試指南

## 問題分析

### 錯誤詳情
```
Error 1062 (23000): Duplicate entry '0000-00-00-recurring-2025-08-31 09:09:28' for key 'unique_specific_date'
```

### 根本原因
1. **軟刪除機制**：使用 `deleted_at` 欄位標記刪除，而不是真正刪除記錄
2. **約束設計問題**：唯一約束包含 `deleted_at` 欄位
3. **批量刪除衝突**：多個記錄同時被軟刪除時，`deleted_at` 設定為相同時間戳
4. **約束衝突**：相同的 `(date/day_of_week, type, deleted_at)` 組合違反唯一約束

### 具體場景
當批量刪除多個常駐設定時：
- 記錄 A：`(NULL, recurring, 2025-08-31 09:09:28)`
- 記錄 B：`(NULL, recurring, 2025-08-31 09:09:28)`
- 記錄 C：`(NULL, recurring, 2025-08-31 09:09:28)`

這些記錄的約束值相同，導致衝突。

## 修復方案

### 1. 資料庫約束修改
**移除有問題的唯一約束**：
```sql
ALTER TABLE `counsel_available_times` 
DROP INDEX `unique_specific_date`,
DROP INDEX `unique_recurring_day`;
```

**添加效能索引**：
```sql
ALTER TABLE `counsel_available_times`
ADD INDEX `idx_date_type_deleted` (`date`, `type`, `deleted_at`),
ADD INDEX `idx_day_type_deleted` (`day_of_week`, `type`, `deleted_at`);
```

### 2. 應用層唯一性保證
**現有的重複檢查邏輯**：
```go
// 固定日期重複檢查
conn.Model(&CounselAvailableTime{}).
    Where("date = ? AND type = ? AND deleted_at IS NULL", date, "specific").
    Count(&existingCount)

// 常駐設定重複檢查  
conn.Model(&CounselAvailableTime{}).
    Where("day_of_week = ? AND type = ? AND deleted_at IS NULL", dayOfWeek, "recurring").
    Count(&existingCount)
```

### 3. 優勢
- **避免軟刪除衝突**：不再有 `deleted_at` 相關的約束問題
- **保持業務邏輯**：應用層確保未刪除記錄的唯一性
- **提升查詢效能**：新增的索引提升查詢速度
- **向後相容**：不影響現有功能

## 測試步驟

### 第一步：執行資料庫遷移

```bash
# 執行新的遷移
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" up

# 檢查約束是否正確移除
mysql -u username -p database_name -e "SHOW INDEX FROM counsel_available_times;"
```

**預期結果**：
- `unique_specific_date` 和 `unique_recurring_day` 約束已移除
- `idx_date_type_deleted` 和 `idx_day_type_deleted` 索引已添加

### 第二步：單項刪除測試

#### 2.1 刪除固定日期設定
1. 選擇一個固定日期設定
2. 執行刪除操作
3. **預期結果**：
   - 刪除成功
   - 記錄的 `deleted_at` 被設定
   - 列表中不再顯示該記錄

#### 2.2 刪除常駐設定
1. 選擇一個常駐設定
2. 執行刪除操作
3. **預期結果**：
   - 刪除成功
   - 無約束衝突錯誤

### 第三步：批量刪除測試

#### 3.1 批量刪除固定日期設定
1. 選擇多個固定日期設定（3-5個）
2. 執行批量刪除
3. **預期結果**：
   - 所有選中的記錄都成功刪除
   - 沒有約束衝突錯誤
   - 列表正確更新

#### 3.2 批量刪除常駐設定
1. 選擇多個常駐設定（3-5個）
2. 執行批量刪除
3. **預期結果**：
   - 所有選中的記錄都成功刪除
   - 沒有 `Error 1062` 錯誤
   - 操作順利完成

#### 3.3 混合類型批量刪除
1. 同時選擇固定日期和常駐設定
2. 執行批量刪除
3. **預期結果**：
   - 所有類型的記錄都成功刪除
   - 無約束衝突

### 第四步：大量資料測試

#### 4.1 創建測試資料
1. 創建大量的測試設定（20-30個）
2. 包含固定日期和常駐設定

#### 4.2 大批量刪除測試
1. 選擇大量記錄（15-20個）
2. 執行批量刪除
3. **檢查**：
   - 所有記錄都成功刪除
   - 操作時間合理
   - 無資料庫錯誤

### 第五步：唯一性檢查測試

#### 5.1 重複創建測試
1. 刪除一個固定日期設定
2. 嘗試創建相同日期的新設定
3. **預期結果**：
   - 創建成功（因為原記錄已軟刪除）
   - 應用層邏輯正確處理

#### 5.2 軟刪除後的重複檢查
1. 軟刪除一個常駐設定
2. 嘗試創建相同星期的新常駐設定
3. **預期結果**：
   - 創建成功
   - 重複檢查邏輯正確

### 第六步：效能測試

#### 6.1 查詢效能測試
1. 在有大量資料的情況下測試
2. 執行重複檢查查詢
3. **檢查**：
   - 查詢速度合理
   - 新索引發揮作用

#### 6.2 刪除效能測試
1. 測試大批量刪除的效能
2. **檢查**：
   - 刪除操作時間合理
   - 無效能瓶頸

### 第七步：資料一致性測試

#### 7.1 軟刪除狀態檢查
```sql
-- 檢查軟刪除的記錄
SELECT id, date, day_of_week, type, deleted_at 
FROM counsel_available_times 
WHERE deleted_at IS NOT NULL;
```

#### 7.2 唯一性驗證
```sql
-- 檢查未刪除記錄的唯一性
SELECT date, type, COUNT(*) 
FROM counsel_available_times 
WHERE deleted_at IS NULL AND type = 'specific'
GROUP BY date, type 
HAVING COUNT(*) > 1;

SELECT day_of_week, type, COUNT(*) 
FROM counsel_available_times 
WHERE deleted_at IS NULL AND type = 'recurring'
GROUP BY day_of_week, type 
HAVING COUNT(*) > 1;
```

**預期結果**：兩個查詢都應該返回空結果

### 第八步：回滾測試

#### 8.1 測試遷移回滾
```bash
# 回滾遷移
migrate -path database/migrations -database "mysql://connection" down 1

# 檢查約束是否恢復
mysql -u username -p database_name -e "SHOW INDEX FROM counsel_available_times;"
```

#### 8.2 重新應用遷移
```bash
# 重新應用遷移
migrate -path database/migrations -database "mysql://connection" up
```

## 監控要點

### 1. 錯誤監控
- 監控是否還有 `Error 1062` 錯誤
- 檢查其他可能的約束衝突

### 2. 效能監控
- 監控查詢效能
- 檢查索引使用情況

### 3. 資料完整性監控
- 定期檢查重複記錄
- 驗證軟刪除狀態

## 成功指標

- [ ] 批量刪除不再出現約束衝突錯誤
- [ ] 單項刪除功能正常
- [ ] 大量資料批量刪除正常
- [ ] 混合類型批量刪除正常
- [ ] 應用層唯一性檢查正常
- [ ] 查詢效能良好
- [ ] 資料一致性保持
- [ ] 遷移可正常回滾

## 長期維護建議

### 1. 定期清理
考慮定期清理軟刪除的舊記錄：
```sql
-- 清理 6 個月前的軟刪除記錄
DELETE FROM counsel_available_times 
WHERE deleted_at IS NOT NULL 
AND deleted_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

### 2. 監控查詢
定期檢查慢查詢日誌，確保索引效果良好。

### 3. 資料驗證
定期執行資料一致性檢查，確保沒有重複的未刪除記錄。

這個修復徹底解決了批量刪除時的約束衝突問題，同時保持了資料的完整性和系統的效能。
