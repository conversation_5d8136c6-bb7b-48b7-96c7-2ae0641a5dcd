# 輔導預約 UI 重新設計測試指南

## 修改內容

已重新設計前端的直接預約輔導功能，參考原有的課程輔導預約樣式，並整合兩種預約方式到統一的選項介面中。

### 主要改進

#### 1. 統一的選項介面
**新設計**：
- 使用與 `#avail-table` 相同的樣式
- 表格形式展示所有輔導選項
- 包含「課程輔導預約」和其他輔導項目

#### 2. 兩種預約方式整合
**課程輔導預約**：
- 使用原有的預約流程
- `counsel_type_id` 為 NULL
- 需要選擇已購買的課程

**直接預約輔導**：
- 使用新的預約流程
- 有明確的 `counsel_type_id`
- 根據輔導項目設定進行預約

#### 3. 簡化的預約流程
**步驟優化**：
1. 選擇輔導項目（表格選項）
2. 選擇日期和時長
3. 選擇具體時間
4. 提交預約

## UI 設計特色

### 1. 輔導項目選擇表格
```html
<table class="table table-responsive reflow-table reflow-table-w-50 reflow-table-sm">
    <thead>
        <tr>
            <th>輔導項目</th>
            <th>描述</th>
            <th>可用時長</th>
            <th>預約</th>
        </tr>
    </thead>
    <tbody>
        <!-- 課程輔導預約選項 -->
        <tr>
            <td>課程輔導預約</td>
            <td>針對已購買課程的專業輔導</td>
            <td>依課程設定</td>
            <td><button @click="selectCourseBooking">預約</button></td>
        </tr>
        <!-- 其他輔導項目 -->
        <tr v-for="type in counselTypes">...</tr>
    </tbody>
</table>
```

### 2. 統一的視覺風格
- **表格樣式**：與原有課程輔導預約保持一致
- **按鈕樣式**：使用 `btn-gold` 保持品牌一致性
- **響應式設計**：支援各種螢幕尺寸
- **動畫效果**：使用 AOS 動畫增強體驗

### 3. 智能導航
- **Fancybox 整合**：使用彈出視窗展示選項
- **流程引導**：選擇後自動跳轉到相應的預約流程
- **返回機制**：可以輕鬆返回選項頁面

## 測試步驟

### 第一步：介面載入測試

#### 1.1 頁面訪問
1. 訪問 `/member/counsel`
2. 檢查頁面正常載入
3. 確認兩個預約按鈕都正確顯示

**預期結果**：
- 「直接預約輔導」按鈕（藍色）
- 「課程輔導預約」按鈕（金色）
- 按鈕樣式與原設計一致

#### 1.2 選項表格顯示
1. 點擊「直接預約輔導」按鈕
2. 檢查 Fancybox 彈出視窗
3. 確認表格正確顯示

**預期結果**：
- Fancybox 正常彈出
- 表格樣式與 `#avail-table` 一致
- 包含「課程輔導預約」選項
- 顯示所有可用的輔導項目

### 第二步：課程輔導預約測試

#### 2.1 選擇課程輔導預約
1. 在選項表格中點擊「課程輔導預約」的預約按鈕
2. 檢查是否正確跳轉到原有的課程輔導預約介面

**預期結果**：
- 關閉選項表格
- 打開原有的 `#avail-table` 介面
- 顯示已購買的課程列表
- 功能與原有流程完全一致

#### 2.2 課程輔導預約流程
1. 選擇一個已購買的課程
2. 選擇可用的時間段
3. 填寫預約資訊
4. 提交預約

**預期結果**：
- 所有原有功能正常運作
- 預約成功後正確儲存
- `counsel_type_id` 為 NULL
- 關聯正確的課程 ID

### 第三步：直接預約輔導測試

#### 3.1 選擇輔導項目
1. 在選項表格中選擇一個輔導項目
2. 點擊對應的預約按鈕

**預期結果**：
- 關閉選項表格
- 打開直接預約模態框
- 模態框標題顯示選中的輔導項目名稱
- 表單已預填輔導項目 ID

#### 3.2 直接預約流程
1. 選擇預約日期
2. 選擇輔導時長
3. 選擇具體時間
4. 選擇關聯課程（可選）
5. 提交預約

**預期結果**：
- 日期選項正確載入
- 時長選項根據輔導項目設定顯示
- 時間選項根據日期和時長動態計算
- 預約成功提交
- `counsel_type_id` 正確設定

### 第四步：資料驗證測試

#### 4.1 課程輔導預約資料
```sql
-- 檢查課程輔導預約記錄
SELECT id, counsel_type_id, mem_pro_id, appointment_date, appointment_time
FROM counsel_appointments 
WHERE counsel_type_id IS NULL
ORDER BY id DESC LIMIT 5;
```

**預期結果**：
- `counsel_type_id` 為 NULL
- `mem_pro_id` 有正確的課程 ID
- 其他欄位正確填入

#### 4.2 直接預約輔導資料
```sql
-- 檢查直接預約記錄
SELECT id, counsel_type_id, mem_pro_id, appointment_date, appointment_time, requested_duration
FROM counsel_appointments 
WHERE counsel_type_id IS NOT NULL
ORDER BY id DESC LIMIT 5;
```

**預期結果**：
- `counsel_type_id` 有正確的輔導項目 ID
- `requested_duration` 正確設定
- `mem_pro_id` 可能為 NULL 或有關聯課程 ID

### 第五步：使用者體驗測試

#### 5.1 導航流程測試
1. 從選項表格 → 課程輔導預約 → 返回
2. 從選項表格 → 直接預約 → 返回
3. 在不同預約方式間切換

**預期結果**：
- 導航流程順暢
- 無頁面跳轉錯誤
- 狀態正確保持

#### 5.2 響應式設計測試
1. 在不同螢幕尺寸下測試
2. 檢查表格響應式行為
3. 確認按鈕和文字可讀性

**預期結果**：
- 在手機上正常顯示
- 表格正確重排
- 按鈕大小適中

#### 5.3 錯誤處理測試
1. 在沒有輔導項目時點擊預約
2. 在沒有可用日期時嘗試預約
3. 測試網路錯誤情況

**預期結果**：
- 適當的錯誤提示
- 不會出現 JavaScript 錯誤
- 使用者能理解問題所在

### 第六步：相容性測試

#### 6.1 瀏覽器相容性
1. 在 Chrome、Firefox、Safari 中測試
2. 檢查 Fancybox 功能
3. 確認 Vue.js 功能正常

#### 6.2 功能完整性
1. 確認原有功能不受影響
2. 檢查新功能與舊功能的整合
3. 驗證資料庫相容性

### 第七步：效能測試

#### 7.1 載入速度
1. 測試頁面初始載入時間
2. 檢查 Fancybox 彈出速度
3. 確認 AJAX 請求回應時間

#### 7.2 記憶體使用
1. 檢查是否有記憶體洩漏
2. 確認 Vue.js 組件正確銷毀
3. 驗證長時間使用的穩定性

## 成功指標

- [ ] 選項表格正確顯示所有輔導項目
- [ ] 「課程輔導預約」選項正常運作
- [ ] 直接預約輔導流程順暢
- [ ] 兩種預約方式資料正確儲存
- [ ] UI 樣式與原設計一致
- [ ] 響應式設計正常運作
- [ ] Fancybox 整合無問題
- [ ] 導航流程直觀易用
- [ ] 錯誤處理機制完善
- [ ] 相容性良好

## 資料庫相容性

### 1. 課程輔導預約
```sql
-- 資料結構保持不變
counsel_type_id: NULL
mem_pro_id: 有值（關聯的課程 ID）
```

### 2. 直接預約輔導
```sql
-- 新的資料結構
counsel_type_id: 有值（輔導項目 ID）
mem_pro_id: 可能為 NULL 或有關聯課程 ID
requested_duration: 有值（請求的時長）
```

### 3. 向後相容性
- 現有的課程輔導預約記錄不受影響
- 新的直接預約記錄使用新的欄位
- 查詢邏輯可以區分兩種類型

## 後續優化建議

### 1. 使用者體驗
- 添加預約歷史的類型標識
- 提供更詳細的預約確認資訊
- 增加預約提醒功能

### 2. 管理功能
- 後台管理介面區分兩種預約類型
- 提供統計報表功能
- 增加預約衝突檢查

### 3. 效能優化
- 快取輔導項目資料
- 優化可用時間計算
- 減少不必要的 API 請求

這個重新設計成功整合了兩種預約方式，提供了統一且直觀的使用者介面，同時保持了與現有系統的完全相容性。
