# 輔導預約時段選擇功能調試總結

## 🐛 問題描述

在前台會員輔導頁面中，`readyToScheduleAppointments` 有資料但區域沒有顯示，且 `title`、`apply_at` 等資訊顯示空白。

## 🔍 問題分析與修正

### 1. 前端插值語法問題

#### 問題
在第 437-438 行，`formatDate` 和 `formatTime` 方法前面缺少了 `this.`：

```javascript
// 錯誤的寫法
${ formatDate(appointment.apply_at, '/') }
${ formatTime(appointment.apply_at, false) }
```

#### 修正
```javascript
// 正確的寫法
${ this.formatDate(appointment.apply_at, '/') }
${ this.formatTime(appointment.apply_at, false) }
```

### 2. 後端資料類型問題

#### 問題
後端 API 回應的 `ApplyAt` 欄位定義為 `string` 類型，但實際上應該是 `time.Time` 類型以便正確序列化：

```go
// 錯誤的定義
ApplyAt string `json:"apply_at"`
```

#### 修正
```go
// 正確的定義
ApplyAt time.Time `json:"apply_at"`
```

### 3. 調試功能增強

#### 添加調試日誌
```javascript
getReadyToScheduleAppointments() {
    axiosRequest()
        .get('/api/members/counsels/ready-to-schedule')
        .then(res => {
            console.log('Ready to schedule appointments:', res.data) // 調試用
            this.readyToScheduleAppointments = res.data || []
        })
        .catch(err => {
            console.log('Error fetching ready to schedule appointments:', err)
            this.readyToScheduleAppointments = []
        })
}
```

## 🔧 完整的修正內容

### 1. 前端修正 (member.counsel.tmpl)

#### 修正插值語法
```html
<td translate="no">
    <div>
        ${ this.formatDate(appointment.apply_at, '/') }<wbr>
        ${ this.formatTime(appointment.apply_at, false) }
    </div>
</td>
```

#### 添加調試日誌
```javascript
getReadyToScheduleAppointments() {
    axiosRequest()
        .get('/api/members/counsels/ready-to-schedule')
        .then(res => {
            console.log('Ready to schedule appointments:', res.data) // 調試用
            this.readyToScheduleAppointments = res.data || []
        })
        .catch(err => {
            console.log('Error fetching ready to schedule appointments:', err)
            this.readyToScheduleAppointments = []
        })
}
```

### 2. 後端修正 (counsel.go)

#### 修正資料結構
```go
data := []struct {
    ID              uint      `json:"id"`
    Title           string    `json:"title"`
    CounselTypeName string    `json:"counsel_type_name"`
    ApplyAt         time.Time `json:"apply_at"`
    Status          string    `json:"status"`
}{}
```

## 🎯 問題根源分析

### 1. Vue.js 插值語法錯誤
- **原因**: 在 Vue.js 中，方法調用必須使用 `this.` 前綴
- **影響**: 導致 `formatDate` 和 `formatTime` 方法無法正確執行
- **結果**: 日期時間顯示為空白

### 2. 資料類型不匹配
- **原因**: 後端 `ApplyAt` 欄位定義為 `string` 而非 `time.Time`
- **影響**: 可能導致日期格式化問題
- **結果**: 前端無法正確解析日期資料

### 3. 缺少調試資訊
- **原因**: 沒有足夠的調試日誌來追蹤問題
- **影響**: 難以確定資料是否正確載入
- **結果**: 問題排查困難

## 📋 驗證檢查清單

### 1. 前端檢查
- [ ] 確認 `readyToScheduleAppointments` 陣列有資料
- [ ] 確認 `v-if="readyToScheduleAppointments.length > 0"` 條件正確
- [ ] 確認 `this.formatDate` 和 `this.formatTime` 方法可以正常調用
- [ ] 確認 Vue.js delimiters 設定為 `["${", "}"]`

### 2. 後端檢查
- [ ] 確認 API 端點 `/api/members/counsels/ready-to-schedule` 正常運作
- [ ] 確認 SQL 查詢返回正確的資料
- [ ] 確認 JSON 序列化正確
- [ ] 確認日期時間格式正確

### 3. 網路檢查
- [ ] 確認 API 請求成功（狀態碼 200）
- [ ] 確認回應資料格式正確
- [ ] 確認沒有 CORS 或其他網路問題

## 🚀 測試步驟

### 1. 瀏覽器開發者工具檢查
```javascript
// 在瀏覽器控制台執行
console.log('readyToScheduleAppointments:', app.readyToScheduleAppointments)
console.log('Length:', app.readyToScheduleAppointments.length)
```

### 2. 網路請求檢查
- 打開瀏覽器開發者工具的 Network 標籤
- 重新載入頁面
- 檢查 `/api/members/counsels/ready-to-schedule` 請求
- 確認回應資料格式

### 3. Vue.js 資料檢查
```javascript
// 檢查 Vue.js 實例資料
console.log('Vue app data:', app.$data)
console.log('formatDate method:', typeof app.formatDate)
console.log('formatTime method:', typeof app.formatTime)
```

## 🔄 後續改善建議

### 1. 錯誤處理增強
```javascript
getReadyToScheduleAppointments() {
    axiosRequest()
        .get('/api/members/counsels/ready-to-schedule')
        .then(res => {
            if (res.data && Array.isArray(res.data)) {
                this.readyToScheduleAppointments = res.data
                console.log(`Loaded ${res.data.length} ready appointments`)
            } else {
                console.warn('Invalid data format:', res.data)
                this.readyToScheduleAppointments = []
            }
        })
        .catch(err => {
            console.error('Failed to fetch ready appointments:', err)
            this.readyToScheduleAppointments = []
            // 可以添加用戶友善的錯誤提示
        })
}
```

### 2. 資料驗證
```javascript
// 在模板中添加資料驗證
<tr v-for="appointment in readyToScheduleAppointments" :key="appointment.id">
    <td>
        <div class="fw-bold">
            ${ appointment.title || '未命名輔導' }
        </div>
    </td>
    <td translate="no">
        <div v-if="appointment.apply_at">
            ${ this.formatDate(appointment.apply_at, '/') }<wbr>
            ${ this.formatTime(appointment.apply_at, false) }
        </div>
        <div v-else class="text-muted">無申請時間</div>
    </td>
</tr>
```

### 3. 載入狀態指示
```html
<!-- 添加載入狀態 -->
<div v-if="loadingReadyAppointments" class="text-center py-3">
    <i class="fas fa-spinner fa-spin me-2"></i>載入中...
</div>

<!-- 無資料提示 -->
<div v-else-if="readyToScheduleAppointments.length === 0" class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>
    目前沒有可選擇預約時段的輔導
</div>
```

## 📝 總結

主要問題是前端 Vue.js 插值語法錯誤，缺少 `this.` 前綴導致方法無法正確執行。修正後應該能夠正常顯示輔導預約資料和時間資訊。

建議在修正後進行完整測試，確保：
1. 區域正確顯示
2. 資料正確載入
3. 時間格式正確
4. 操作按鈕正常運作

如果問題仍然存在，可以通過瀏覽器開發者工具進一步調試，檢查具體的錯誤訊息和資料流。
