# 綠界金流串接功能說明

## 概述

本系統已整合綠界金流 (ECPay) 支付功能，支援模擬和實際金流環境。

## 功能特色

1. **模擬金流環境**：使用綠界提供的測試環境進行開發和測試
2. **實際金流環境**：支援正式環境的金流處理
3. **自動點數配發**：付款成功後自動為會員增加點數
4. **交易記錄**：完整記錄所有綠界金流交易資料
5. **通知處理**：處理綠界金流的付款通知回調

## 實現的功能

### 1. 新增的文件

- `app/models/ecpay.go` - 綠界金流交易記錄模型
- `app/utils/ecpay.go` - 綠界金流工具函數
- `database/migrations/000100_create_ecpay_orders_table.up.mysql` - 數據庫遷移文件
- `test/ecpay_test.go` - 測試文件

### 2. 修改的文件

- `app/controllers/api/front_api/stored.go` - 主要的儲值控制器
- `routes/front_routes.go` - 添加綠界金流通知路由

### 3. 新增的函數

#### stored.go 中的新函數：

- `ecpayTest(order *Order) string` - 綠界金流測試環境
- `getEcpayForm(opt *PaymentOption, order *Order) string` - 綠界金流正式環境
- `NotifyEcpayOrder(c *gin.Context)` - 綠界金流通知處理
- `IsEcpayTradePaid(rtnCode string) bool` - 判斷交易是否成功
- `getDomain() string` - 取得網域名稱

## 配置說明

### 測試環境常量

```go
const (
    EcpayTestMerchantID = "3366242"
    EcpayTestHashKey    = "aHk46nU5rBAIS7kp"
    EcpayTestHashIV     = "hpOOGBqq2jracs3x"
)
```

### 支付選項配置

在後端管理系統中，需要設定支付選項：

1. 支付方式 (payment_method): `ecpay`
2. 商戶ID (merchant_id): 綠界提供的商戶編號
3. HashKey (hash_key): 綠界提供的 HashKey
4. HashIV (hash_iv): 綠界提供的 HashIV
5. 是否模擬 (is_simulate): 是否使用測試環境

## API 端點

### 綠界金流通知端點

- **URL**: `/api/ecpay/order/notify`
- **方法**: POST
- **用途**: 接收綠界金流的付款通知

## 使用流程

1. **會員選擇儲值方案**：在前端選擇要儲值的點數方案
2. **選擇綠界金流**：選擇綠界金流作為付款方式
3. **生成付款表單**：系統生成綠界金流的付款表單
4. **跳轉到綠界**：會員被導向綠界金流付款頁面
5. **完成付款**：會員在綠界完成付款
6. **接收通知**：系統接收綠界的付款通知
7. **更新訂單**：系統更新訂單狀態並配發點數

## 數據庫結構

### ecpay_orders 表

存儲綠界金流的交易記錄：

- `order_no`: 訂單編號
- `rtn_code`: 回傳代碼
- `rtn_msg`: 回傳訊息
- `merchant_id`: 商戶編號
- `trade_no`: 綠界交易編號
- `trade_amt`: 交易金額
- `payment_type`: 付款方式
- 其他相關欄位...

## 注意事項

1. **安全性**：確保 HashKey 和 HashIV 的安全性
2. **驗證**：目前簡化了 CheckMacValue 驗證，實際部署時需要實現完整驗證
3. **錯誤處理**：需要完善錯誤處理機制
4. **日誌記錄**：建議添加詳細的日誌記錄
5. **測試**：部署前需要充分測試模擬和正式環境

## 後續改進

1. 實現完整的 CheckMacValue 驗證
2. 添加更詳細的錯誤處理
3. 實現退款功能
4. 添加交易查詢功能
5. 完善日誌記錄系統
