# 輔導時間設定狀態即時編輯功能測試指南

## 修改內容

已修改 `counsel.available_times.tmpl`，移除備註欄位並將狀態改為可直接編輯的下拉選單，讓使用者可以在列表頁面直接修改設定的狀態。

### 主要修改

#### 1. 移除備註欄位
**修改前**：
- 表格包含備註欄位
- 顯示每個設定的備註內容

**修改後**：
- 移除備註欄位
- 簡化表格結構，提升瀏覽效率

#### 2. 狀態即時編輯
**修改前**：
- 狀態顯示為文字標籤（啟用/停用）
- 需要進入編輯頁面才能修改狀態

**修改後**：
- 狀態顯示為下拉選單
- 可直接在列表頁面修改狀態
- 修改後自動保存

#### 3. 表格結構優化
**欄位調整**：
- 移除：備註欄位
- 修改：狀態欄位（文字 → 下拉選單）
- 保留：類型、日期/星期、可預約時間段、建立時間、操作

## 功能特色

### 1. 即時狀態切換
```html
<select v-model="item.status" @change="updateStatus(item)" class="form-select form-select-sm">
    <option value="Y">啟用</option>
    <option value="N">停用</option>
</select>
```

### 2. 自動保存機制
```javascript
updateStatus(item) {
    // 準備完整的更新資料
    const updateData = {
        type: item.type,
        status: item.status,
        note: item.note,
        time_slot_options: item.time_slot_options
    }

    // 根據類型添加相應欄位
    if (item.type === 'specific') {
        updateData.date = item.date
    } else if (item.type === 'recurring') {
        updateData.day_of_week = item.day_of_week
    }

    // 發送更新請求
    axiosRequest('json')
        .patch(`/api/admin/counsel-available-times/${item.id}`, updateData)
        .then(res => {
            msgTopSuccess('狀態更新成功')
        })
        .catch(err => {
            msgError(err.response.data.msg)
            this.getData() // 失敗時重新載入資料
        })
}
```

### 3. 錯誤處理
- 更新成功：顯示成功訊息
- 更新失敗：顯示錯誤訊息並恢復原狀態

## 測試步驟

### 第一步：頁面載入測試

#### 1.1 檢查表格結構
1. 訪問 `/admin/counsel/available-times`
2. **檢查表格標題**：
   - ✅ 類型
   - ✅ 日期/星期
   - ✅ 可預約時間段
   - ✅ 狀態
   - ✅ 建立時間
   - ✅ 操作
   - ❌ 備註（已移除）

#### 1.2 檢查狀態欄位
1. 確認狀態欄位顯示為下拉選單
2. 檢查下拉選單選項：
   - 啟用（Y）
   - 停用（N）
3. 確認當前狀態正確選中

### 第二步：狀態切換功能測試

#### 2.1 啟用 → 停用測試
1. 找到一個狀態為「啟用」的設定
2. 點擊狀態下拉選單
3. 選擇「停用」
4. **預期結果**：
   - 顯示「狀態更新成功」訊息
   - 下拉選單保持在「停用」狀態
   - 資料庫中的狀態已更新

#### 2.2 停用 → 啟用測試
1. 找到一個狀態為「停用」的設定
2. 點擊狀態下拉選單
3. 選擇「啟用」
4. **預期結果**：
   - 顯示「狀態更新成功」訊息
   - 下拉選單保持在「啟用」狀態
   - 資料庫中的狀態已更新

#### 2.3 多個設定測試
1. 連續修改多個設定的狀態
2. 檢查每次修改都正確保存
3. 重新整理頁面確認狀態持久化

### 第三步：不同類型設定測試

#### 3.1 固定日期設定狀態修改
1. 找到類型為「固定日期」的設定
2. 修改其狀態
3. **檢查 API 請求**：
   - 包含 `type: "specific"`
   - 包含 `date` 欄位
   - 不包含 `day_of_week` 欄位

#### 3.2 常駐設定狀態修改
1. 找到類型為「常駐設定」的設定
2. 修改其狀態
3. **檢查 API 請求**：
   - 包含 `type: "recurring"`
   - 包含 `day_of_week` 欄位
   - 不包含 `date` 欄位

### 第四步：錯誤處理測試

#### 4.1 網路錯誤模擬
1. 中斷網路連線
2. 嘗試修改狀態
3. **預期結果**：
   - 顯示錯誤訊息
   - 狀態恢復到修改前的值

#### 4.2 伺服器錯誤模擬
1. 修改一個不存在的設定 ID（透過開發者工具）
2. **預期結果**：
   - 顯示「查無資料」錯誤訊息
   - 頁面重新載入資料

### 第五步：使用者體驗測試

#### 5.1 操作流暢性
1. 快速連續修改多個設定的狀態
2. 檢查是否有延遲或卡頓
3. 確認每次修改都有適當的回饋

#### 5.2 視覺回饋
1. 修改狀態時檢查載入指示
2. 確認成功/失敗訊息清楚可見
3. 檢查下拉選單的視覺狀態

#### 5.3 鍵盤操作
1. 使用 Tab 鍵導航到狀態下拉選單
2. 使用方向鍵選擇選項
3. 使用 Enter 確認選擇

### 第六步：資料一致性測試

#### 6.1 前端狀態同步
1. 修改設定狀態
2. 不重新整理頁面，檢查前端顯示
3. 重新整理頁面，確認狀態持久化

#### 6.2 前端預約功能影響
1. 將某個設定狀態改為「停用」
2. 訪問前端預約頁面
3. 確認該設定不出現在可預約日期中
4. 將狀態改回「啟用」
5. 確認該設定重新出現在可預約日期中

### 第七步：效能測試

#### 7.1 大量資料測試
1. 在有大量設定的情況下測試
2. 檢查頁面載入速度
3. 檢查狀態修改的回應速度

#### 7.2 併發修改測試
1. 開啟多個瀏覽器視窗
2. 同時修改不同設定的狀態
3. 檢查是否有衝突或資料不一致

## API 請求格式驗證

### 固定日期設定更新
```json
PATCH /api/admin/counsel-available-times/{id}
{
    "type": "specific",
    "date": "2025-08-25",
    "status": "N",
    "note": "...",
    "time_slot_options": [...]
}
```

### 常駐設定更新
```json
PATCH /api/admin/counsel-available-times/{id}
{
    "type": "recurring",
    "day_of_week": 1,
    "status": "Y",
    "note": "...",
    "time_slot_options": [...]
}
```

## 成功指標

- [ ] 備註欄位已完全移除
- [ ] 狀態顯示為可編輯的下拉選單
- [ ] 狀態修改即時生效
- [ ] 成功/失敗訊息正確顯示
- [ ] 錯誤處理機制正常
- [ ] 不同類型設定都能正確更新
- [ ] API 請求格式正確
- [ ] 前端預約功能正確反映狀態變更
- [ ] 使用者體驗流暢
- [ ] 資料一致性良好

## 使用者體驗改善

### 1. 操作效率提升
- **修改前**：需要點擊編輯 → 修改狀態 → 保存 → 返回列表
- **修改後**：直接在列表頁面點擊下拉選單修改

### 2. 頁面簡化
- 移除不常用的備註欄位
- 表格更簡潔，重點突出

### 3. 即時回饋
- 修改後立即顯示結果
- 清楚的成功/失敗訊息

這個修改大幅提升了管理員的操作效率，讓狀態管理變得更加直觀和便捷。
