# 活動贈送輔導功能更新

## 更新內容

根據需求，已對活動贈送輔導功能進行以下修改：

### ✅ 1. 移除新增時的提示訊息
- **修改位置**: `addFreeCounseling()` 方法
- **變更內容**: 
  - 移除 `msgError()` 和 `msgSuccess()` 提示
  - 點擊新增按鈕時直接新增，不顯示任何提示訊息
  - 如果未選擇輔導類型或數量，靜默忽略操作

### ✅ 2. 新增後的輔導可調整數量
- **修改位置**: 表格的數量欄位
- **變更內容**:
  - 將原本的純文字顯示改為下拉選單
  - 使用 `v-model.number="counsel.amount"` 雙向綁定
  - 支援 1-10 的數量選擇
  - 樣式使用 `form-select-sm` 和固定寬度 80px

### ✅ 3. 已新增的輔導在選單中隱藏
- **修改位置**: 輔導類型選擇下拉選單
- **變更內容**:
  - 新增 `availableCounselTypes` 計算屬性
  - 自動過濾掉已新增的輔導類型
  - 防止重複新增相同的輔導類型

## 技術實現

### 修改的方法

#### 1. addFreeCounseling() 方法
```javascript
addFreeCounseling() {
    if (!this.selectedCounselType) {
        return  // 靜默返回，不顯示錯誤
    }
    
    if (!this.selectedCounselAmount || this.selectedCounselAmount <= 0) {
        return  // 靜默返回，不顯示錯誤
    }
    
    // 檢查是否已經存在相同的輔導類型
    const existingIndex = this.item.free_counselings.findIndex(item => item.counsel_type_id == this.selectedCounselType)
    
    if (existingIndex === -1) {
        // 如果不存在，新增（移除了成功提示）
        this.item.free_counselings.push({
            activity_id: this.item.id,
            counsel_type_id: this.selectedCounselType,
            amount: this.selectedCounselAmount
        })
    }
    
    // 重置選擇
    this.selectedCounselType = ''
    this.selectedCounselAmount = 1
}
```

#### 2. removeFreeCounseling() 方法
```javascript
removeFreeCounseling(index) {
    this.item.free_counselings.splice(index, 1)
    // 移除了成功提示
}
```

#### 3. availableCounselTypes 計算屬性
```javascript
availableCounselTypes() {
    // 過濾掉已經新增的輔導類型
    const addedCounselTypeIds = this.item.free_counselings.map(counsel => counsel.counsel_type_id)
    return this.counselTypesList.filter(counselType => !addedCounselTypeIds.includes(counselType.id))
}
```

### UI 變更

#### 1. 輔導類型選擇
```html
<select v-model="selectedCounselType" class="form-select">
    <option value="">請選擇輔導類型</option>
    <option v-for="counselType in availableCounselTypes" :value="counselType.id" v-html="counselType.name"></option>
</select>
```

#### 2. 數量調整
```html
<td>
    <select v-model.number="counsel.amount" class="form-select form-select-sm" style="width: 80px;">
        <option v-for="i in 10" :value="i">${i}</option>
    </select>
</td>
```

## 使用流程

### 新增輔導
1. 選擇輔導類型
2. 選擇數量
3. 點擊「新增」按鈕
4. 輔導直接新增到表格中（無提示訊息）
5. 該輔導類型從選擇列表中消失

### 調整數量
1. 在表格中找到要調整的輔導
2. 點擊數量欄位的下拉選單
3. 選擇新的數量
4. 數量立即更新

### 刪除輔導
1. 點擊要刪除的輔導的垃圾桶按鈕
2. 輔導立即從表格中移除（無提示訊息）
3. 該輔導類型重新出現在選擇列表中

## 優勢

1. **更流暢的操作體驗**: 移除不必要的提示訊息，操作更直觀
2. **即時數量調整**: 可以直接在表格中修改數量，無需刪除重新新增
3. **防止重複**: 已新增的輔導類型自動從選擇列表中隱藏
4. **響應式更新**: 新增/刪除操作會即時更新可選擇的輔導類型列表

## 測試建議

1. 測試新增輔導功能（確認無提示訊息）
2. 測試數量調整功能
3. 測試刪除輔導功能（確認無提示訊息）
4. 驗證輔導類型選單的動態過濾
5. 測試邊界情況（未選擇類型時點擊新增）

## 相容性

- ✅ 與現有功能完全相容
- ✅ 不影響其他模組
- ✅ 保持原有的數據結構
- ✅ 支援所有瀏覽器
