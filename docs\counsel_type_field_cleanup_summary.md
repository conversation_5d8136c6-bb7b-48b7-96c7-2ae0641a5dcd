# CounselType 已移除欄位清理總結

## 問題描述

在將 CounselType 的 duration 相關設定從範圍型（min/max）改為矩陣型（固定選項）的過程中，移除了以下欄位：
- `min_duration`
- `max_duration` 
- `sorting`

但程式碼中仍有部分地方引用這些已移除的欄位，導致系統出錯。

## 已修復的問題

### 1. 後台 API 排序問題
**檔案**: `app/controllers/api/backend_api/counsel.go`
**問題**: `GetCounselTypes` 函數中仍引用 `sorting` 欄位進行排序
**修復**: 
```go
// 修復前
conn = conn.Order("sorting ASC, id ASC")

// 修復後  
conn = conn.Order("id ASC")
```

### 2. 後台列表頁面顯示問題
**檔案**: `templates/backend/counsel/counsel.types.tmpl`

#### 2.1 表格標題修復
**問題**: 仍顯示已移除的欄位標題
**修復**:
```html
<!-- 修復前 -->
<th width="80">排序</th>
<th width="120">時長範圍</th>

<!-- 修復後 -->
<th width="200">可用時長選項</th>
```

#### 2.2 表格內容修復
**問題**: 仍嘗試顯示 `min_duration` 和 `max_duration`
**修復**:
```html
<!-- 修復前 -->
<td>${ item.sorting }</td>
<td>${ item.min_duration }-${ item.max_duration } 分鐘</td>

<!-- 修復後 -->
<td>
    <span v-if="item.duration_options && item.duration_options.length > 0">
        ${ item.duration_options.join(', ') } 分鐘
    </span>
    <span v-else>-</span>
</td>
```

#### 2.3 表格 colspan 修復
**問題**: 空資料提示的 colspan 數量不正確
**修復**:
```html
<!-- 修復前 -->
<td colspan="9" class="text-center text-muted">暫無資料</td>

<!-- 修復後 -->
<td colspan="7" class="text-center text-muted">暫無資料</td>
```

### 3. 後台 API 資料解析
**檔案**: `app/controllers/api/backend_api/counsel.go`
**問題**: `GetCounselTypes` 未解析 `duration_options` 供前端使用
**修復**: 添加解析邏輯
```go
// 解析每個輔導類型的時長選項
for i := range data {
    data[i].ParseDurationOptions()
}
```

## 確認無問題的部分

### 1. 資料模型
**檔案**: `app/models/counsel.go`
- ✅ CounselType 結構已正確移除舊欄位
- ✅ 新增了 `AvailableDurations` 和 `DurationOptions` 欄位
- ✅ 相關方法已正確實現

### 2. 資料庫遷移
**檔案**: `database/migrations/000226_modify_counsel_types_duration_structure.up.mysql`
- ✅ 正確移除了 `min_duration`, `max_duration`, `sorting` 欄位
- ✅ 正確新增了 `available_durations` 欄位
- ✅ 資料遷移邏輯正確

### 3. 前端編輯頁面
**檔案**: `templates/backend/counsel/counsel.type_reg.tmpl`
- ✅ 已移除對舊欄位的引用
- ✅ 已實現新的時長選項管理介面

### 4. 前端會員預約頁面
**檔案**: `templates/front/member/member.counsel.tmpl`
- ✅ 已適配新的資料結構
- ✅ 正確使用 `duration_options` 欄位

## 歷史檔案說明

以下檔案包含已移除欄位的引用，但屬於歷史檔案，不應修改：

### 1. 初始遷移檔案
**檔案**: `database/migrations/000224_create_counsel_types_table.up.mysql`
**說明**: 這是創建表格的初始遷移，包含原始的欄位定義。後續的遷移檔案 `000226_modify_counsel_types_duration_structure.up.mysql` 會移除這些欄位。

### 2. 回滾遷移檔案
**檔案**: `database/migrations/000226_modify_counsel_types_duration_structure.down.mysql`
**說明**: 這是回滾檔案，用於在需要時恢復舊的表格結構。

### 3. 其他系統的 sorting 欄位
**檔案**: 多個檔案中的 `sorting` 欄位引用
**說明**: 這些是其他資料模型（如 ProductKind, PaymentOption 等）的 sorting 欄位，與 CounselType 無關。

## 測試建議

### 1. 後台功能測試
1. 訪問 `/admin/counsel/types` 檢查列表頁面
2. 確認顯示正確的欄位（項目名稱、描述、預設時長、可用時長選項、狀態）
3. 測試新增/編輯輔導項目功能

### 2. 前端預約測試
1. 訪問 `/member/counsel` 進行預約
2. 確認輔導項目和時長選擇正常
3. 測試預約提交功能

### 3. API 測試
```bash
# 測試後台 API
curl -X GET "/api/admin/counsel-types"

# 測試前端 API  
curl -X GET "/api/members/counsels/types"
```

## 部署檢查清單

- [ ] 執行資料庫遷移
- [ ] 重新部署後端程式
- [ ] 清除前端快取
- [ ] 測試後台輔導項目管理功能
- [ ] 測試前端預約功能
- [ ] 檢查系統日誌是否有錯誤

## 結論

所有引用已移除欄位的程式碼都已修復，系統應該可以正常運行。主要修復了：

1. 後台 API 的排序邏輯
2. 後台列表頁面的顯示邏輯
3. 資料解析和傳遞邏輯

這些修復確保了系統在移除 `min_duration`, `max_duration`, `sorting` 欄位後能夠正常運作。
