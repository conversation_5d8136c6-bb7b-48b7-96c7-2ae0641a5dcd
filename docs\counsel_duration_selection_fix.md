# 輔導時長選擇功能修正總結

## 🐛 問題描述

原先的實現錯誤地將輔導類型的時長設定為固定顯示預設時長，但實際上應該根據 `available_durations` 設定提供選擇選項。

## ❌ 原先的錯誤實現

### 1. 固定時長顯示
```html
<!-- 錯誤：固定顯示預設時長 -->
<div v-else class="form-control-plaintext">
    <span class="fw-bold">${ getCounselTypeDuration(selectedAppointment.counsel_type_id) }分鐘</span>
    <small class="text-muted d-block">此輔導類型的預設時長</small>
</div>
```

### 2. 硬編碼時長設定
```javascript
// 錯誤：硬編碼的時長設定
counselTypeDurations: {
    1: 60,   // 已(可)預約課後輔導
    2: 30,   // 購課建議
    3: 60,   // 個人進修計畫
    4: 90,   // 獨寵至尊陪練卡
    5: 30,   // 輔導時間凍結卡
    6: 120,  // 陪妳搭時光機
}
```

### 3. 複雜的提交邏輯
```javascript
// 錯誤：複雜的條件判斷
if (this.selectedAppointment.counsel_type_id) {
    duration = this.getCounselTypeDuration(this.selectedAppointment.counsel_type_id)
} else {
    duration = parseInt(this.scheduleForm.duration)
}
```

## ✅ 正確的實現

### 1. 後端 API 修正

#### 添加 available_durations 欄位
```go
data := []struct {
    ID                 uint      `json:"id"`
    Title              string    `json:"title"`
    CounselTypeID      uint      `json:"counsel_type_id"`
    CounselTypeName    string    `json:"counsel_type_name"`
    AvailableDurations string    `json:"available_durations"`  // 新增
    ApplyAt            time.Time `json:"apply_at"`
    Status             string    `json:"status"`
}{}
```

#### 修正 SQL 查詢
```sql
SELECT 
    ca.id,
    ca.title,
    ct.id AS counsel_type_id,
    ct.name AS counsel_type_name,
    ct.available_durations,  -- 新增
    ca.created_at AS apply_at,
    ca.status
FROM counsel_appointments ca
LEFT JOIN counsel_types ct ON ca.counsel_type_id = ct.id
```

### 2. 前端實現修正

#### 統一使用 select 選擇
```html
<!-- 課程輔導預約：顯示預設時長選項 -->
<select v-if="!selectedAppointment?.counsel_type_id" v-model="scheduleForm.duration" class="form-select" required>
    <option value="">請選擇時長</option>
    <option value="30">30分鐘</option>
    <option value="60">60分鐘</option>
    <option value="90">90分鐘</option>
    <option value="120">120分鐘</option>
</select>

<!-- 輔導類型預約：顯示該類型的可選時長 -->
<select v-else v-model="scheduleForm.duration" class="form-select" required>
    <option value="">請選擇時長</option>
    <option v-for="duration in getAvailableDurations(selectedAppointment.available_durations)" 
            :key="duration" :value="duration">
        ${ duration }分鐘
    </option>
</select>
```

#### 解析 available_durations 方法
```javascript
// 解析可選輔導時長
getAvailableDurations(availableDurationsStr) {
    if (!availableDurationsStr) {
        return [60] // 預設 60 分鐘
    }
    
    try {
        // 假設格式為 "30,60,90" 或 JSON 格式
        if (availableDurationsStr.includes(',')) {
            return availableDurationsStr.split(',').map(d => parseInt(d.trim())).filter(d => d > 0)
        } else {
            // 嘗試解析 JSON 格式
            const parsed = JSON.parse(availableDurationsStr)
            if (Array.isArray(parsed)) {
                return parsed.map(d => parseInt(d)).filter(d => d > 0)
            }
        }
    } catch (e) {
        console.warn('Failed to parse available_durations:', availableDurationsStr)
    }
    
    return [60] // 預設 60 分鐘
}
```

#### 簡化提交邏輯
```javascript
// 簡化：統一處理
submitScheduleTime() {
    const scheduleData = {
        appointment_id: this.selectedAppointment.id,
        appointment_date: this.scheduleForm.date,
        appointment_time: this.scheduleForm.time,
        requested_duration: parseInt(this.scheduleForm.duration)  // 統一處理
    }
    
    // 提交邏輯...
}
```

#### 簡化表單驗證
```javascript
// 簡化：統一驗證規則
canSubmitSchedule() {
    return this.scheduleForm.date && 
           this.scheduleForm.time && 
           this.scheduleForm.duration &&  // 所有預約都需要選擇時長
           !this.loadingTimes
}
```

## 🔧 技術改善

### 1. 資料驅動設計
- **修正前**：硬編碼時長設定
- **修正後**：從資料庫 `available_durations` 欄位動態載入

### 2. 統一的用戶界面
- **修正前**：輔導類型顯示固定文字，課程輔導顯示選擇器
- **修正後**：所有預約類型都使用選擇器，保持界面一致性

### 3. 靈活的資料格式支援
- 支援逗號分隔格式：`"30,60,90"`
- 支援 JSON 陣列格式：`"[30,60,90]"`
- 提供預設值處理：當解析失敗時使用 60 分鐘

### 4. 簡化的業務邏輯
- 移除複雜的條件判斷
- 統一的資料處理流程
- 更容易維護和擴展

## 📊 支援的資料格式

### 1. 逗號分隔格式
```
"30,60,90"
```

### 2. JSON 陣列格式
```
"[30,60,90]"
```

### 3. 錯誤處理
- 解析失敗時使用預設值 `[60]`
- 過濾無效值（非正整數）
- 控制台警告訊息

## 🎯 使用者體驗改善

### 1. 一致的操作體驗
- 所有預約類型都使用相同的選擇器界面
- 統一的操作流程和視覺設計

### 2. 靈活的時長選擇
- 根據輔導類型提供相應的時長選項
- 避免不合適的時長選擇

### 3. 清楚的選項顯示
- 明確顯示可選的時長選項
- 避免用戶困惑

## 📋 測試檢查清單

### 1. 後端 API 測試
- [ ] 確認 `available_durations` 欄位正確回傳
- [ ] 測試不同格式的 `available_durations` 資料
- [ ] 確認 SQL 查詢正常執行

### 2. 前端解析測試
- [ ] 測試逗號分隔格式解析
- [ ] 測試 JSON 陣列格式解析
- [ ] 測試錯誤資料的處理

### 3. 用戶界面測試
- [ ] 輔導類型預約正確顯示可選時長
- [ ] 課程輔導預約正確顯示預設時長選項
- [ ] 選擇器正常運作

### 4. 表單驗證測試
- [ ] 所有預約類型都需要選擇時長
- [ ] 表單驗證邏輯正確
- [ ] 提交邏輯正常運作

## 🚀 改善效果

### 1. 資料準確性
- ✅ 時長選項來自資料庫設定，不再硬編碼
- ✅ 支援動態調整時長選項
- ✅ 避免不一致的時長設定

### 2. 系統靈活性
- ✅ 支援多種資料格式
- ✅ 易於擴展和維護
- ✅ 統一的處理邏輯

### 3. 用戶體驗
- ✅ 一致的操作界面
- ✅ 清楚的選項顯示
- ✅ 避免操作困惑

### 4. 代碼品質
- ✅ 移除硬編碼設定
- ✅ 簡化業務邏輯
- ✅ 提高可維護性

## 📝 總結

這次修正解決了輔導時長選擇的根本問題：
1. **從硬編碼改為資料驅動**：時長選項來自資料庫設定
2. **統一用戶界面**：所有預約類型都使用選擇器
3. **簡化業務邏輯**：移除複雜的條件判斷
4. **提高靈活性**：支援多種資料格式和動態調整

現在的實現更符合實際業務需求，提供了更好的用戶體驗和更高的系統靈活性。
