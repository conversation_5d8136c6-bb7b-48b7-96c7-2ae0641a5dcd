# 輔導預約時段表單改善總結

## 🎯 改善目標

根據用戶需求對選擇預約時段表單進行以下改善：
1. 移除備註欄位
2. 改善無可預約時段的提醒顯示
3. 根據輔導類型設定時長，課程輔導預約才使用預設時長選項

## ✅ 已完成的改善

### 1. 移除備註欄位

#### 修改前
```html
<!-- 備註 -->
<div class="mb-4">
    <label class="form-label">
        <i class="fas fa-comment me-2"></i>備註
    </label>
    <textarea v-model="scheduleForm.note" class="form-control" rows="3"
              placeholder="如有特殊需求或說明，請在此填寫"></textarea>
</div>
```

#### 修改後
完全移除備註欄位，簡化表單結構。

### 2. 智能輔導時長設定

#### 新的邏輯設計
```html
<!-- 輔導時長 -->
<div class="mb-4">
    <label class="form-label">
        <i class="fas fa-hourglass-half me-2"></i>輔導時長 <span class="text-danger">*</span>
    </label>
    <!-- 課程輔導預約：顯示預設時長選項 -->
    <select v-if="!selectedAppointment.counsel_type_id" v-model="scheduleForm.duration" class="form-select" required>
        <option value="">請選擇時長</option>
        <option value="30">30分鐘</option>
        <option value="60">60分鐘</option>
        <option value="90">90分鐘</option>
        <option value="120">120分鐘</option>
    </select>
    <!-- 輔導類型預約：顯示該類型的預設時長 -->
    <div v-else class="form-control-plaintext">
        <span class="fw-bold">${ getCounselTypeDuration(selectedAppointment.counsel_type_id) }分鐘</span>
        <small class="text-muted d-block">此輔導類型的預設時長</small>
    </div>
</div>
```

#### 輔導類型時長設定
```javascript
// 輔導類型預設時長設定
counselTypeDurations: {
    1: 60,   // 已(可)預約課後輔導
    2: 30,   // 購課建議
    3: 60,   // 個人進修計畫
    4: 90,   // 獨寵至尊陪練卡
    5: 30,   // 輔導時間凍結卡
    6: 120,  // 陪妳搭時光機
}
```

### 3. 改善無可預約時段提醒

#### 修改前
```html
<div v-if="scheduleForm.date && !loadingTimes && availableTimes.length === 0" 
     class="alert alert-warning text-center">
    <i class="fas fa-exclamation-triangle me-2"></i>
    此日期沒有可預約的時段，請選擇其他日期
</div>
```

#### 修改後
```html
<div v-if="scheduleForm.date && !loadingTimes && availableTimes.length === 0" 
     class="alert alert-warning">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle me-3 text-warning" style="font-size: 1.2em;"></i>
        <div>
            <strong>此日期沒有可預約的時段</strong>
            <div class="small text-muted mt-1">請選擇其他日期，或聯繫客服了解更多可預約時間</div>
        </div>
    </div>
</div>
```

### 4. 資料結構調整

#### 移除 note 欄位
```javascript
// 修改前
scheduleForm: {
    date: '',
    time: '',
    duration: '',
    note: ''
}

// 修改後
scheduleForm: {
    date: '',
    time: '',
    duration: ''
}
```

### 5. 方法實現

#### 獲取輔導類型時長
```javascript
// 獲取輔導類型的預設時長
getCounselTypeDuration(counselTypeId) {
    return this.counselTypeDurations[counselTypeId] || 60
}
```

#### 智能提交邏輯
```javascript
submitScheduleTime() {
    // 決定輔導時長
    let duration
    if (this.selectedAppointment.counsel_type_id) {
        // 輔導類型預約：使用該類型的預設時長
        duration = this.getCounselTypeDuration(this.selectedAppointment.counsel_type_id)
    } else {
        // 課程輔導預約：使用用戶選擇的時長
        duration = parseInt(this.scheduleForm.duration)
    }

    const scheduleData = {
        appointment_id: this.selectedAppointment.id,
        appointment_date: this.scheduleForm.date,
        appointment_time: this.scheduleForm.time,
        requested_duration: duration
    }
    
    // 提交邏輯...
}
```

#### 智能表單驗證
```javascript
canSubmitSchedule() {
    const hasBasicInfo = this.scheduleForm.date && 
                       this.scheduleForm.time && 
                       !this.loadingTimes
    
    // 如果是輔導類型預約，不需要檢查 duration（使用預設值）
    if (this.selectedAppointment && this.selectedAppointment.counsel_type_id) {
        return hasBasicInfo
    }
    
    // 如果是課程輔導預約，需要檢查 duration
    return hasBasicInfo && this.scheduleForm.duration
}
```

## 🔧 技術實現特點

### 1. 條件渲染邏輯
- **輔導類型預約** (`counsel_type_id` 存在)：顯示固定時長，不可選擇
- **課程輔導預約** (`counsel_type_id` 不存在)：顯示時長選擇器

### 2. 智能時長決定
- 根據預約類型自動決定使用預設時長還是用戶選擇的時長
- 確保資料一致性和用戶體驗

### 3. 表單驗證優化
- 根據預約類型調整驗證規則
- 輔導類型預約不需要驗證時長選擇

### 4. 用戶體驗改善
- 更清楚的無可預約時段提醒
- 簡化的表單結構
- 智能的時長設定

## 📊 改善效果

### 1. 表單簡化
- ✅ 移除不必要的備註欄位
- ✅ 減少用戶填寫負擔
- ✅ 提升表單完成率

### 2. 智能化設定
- ✅ 根據輔導類型自動設定時長
- ✅ 避免用戶選擇錯誤的時長
- ✅ 保持業務邏輯一致性

### 3. 用戶體驗提升
- ✅ 更清楚的提醒訊息
- ✅ 更直觀的操作流程
- ✅ 減少操作步驟

### 4. 資料準確性
- ✅ 確保時長設定符合業務規則
- ✅ 避免不合理的時長選擇
- ✅ 提高預約成功率

## 🎯 業務邏輯

### 輔導類型與時長對應
| 輔導類型 | 預設時長 | 說明 |
|---------|---------|------|
| 已(可)預約課後輔導 | 60分鐘 | 標準輔導時長 |
| 購課建議 | 30分鐘 | 簡短諮詢 |
| 個人進修計畫 | 60分鐘 | 詳細規劃討論 |
| 獨寵至尊陪練卡 | 90分鐘 | 深度陪練 |
| 輔導時間凍結卡 | 30分鐘 | 快速處理 |
| 陪妳搭時光機 | 120分鐘 | 長時間陪伴 |

### 預約類型區分
- **輔導類型預約**：有 `counsel_type_id`，使用固定時長
- **課程輔導預約**：無 `counsel_type_id`，用戶可選擇時長

## 📋 測試檢查清單

### 1. 輔導類型預約測試
- [ ] 正確顯示預設時長
- [ ] 不顯示時長選擇器
- [ ] 提交時使用正確的預設時長

### 2. 課程輔導預約測試
- [ ] 正確顯示時長選擇器
- [ ] 必須選擇時長才能提交
- [ ] 提交時使用選擇的時長

### 3. 表單驗證測試
- [ ] 輔導類型預約不需要選擇時長即可提交
- [ ] 課程輔導預約必須選擇時長才能提交
- [ ] 日期和時間仍為必填

### 4. 提醒訊息測試
- [ ] 無可預約時段時正確顯示提醒
- [ ] 提醒訊息內容清楚明確
- [ ] 提醒樣式美觀易讀

## 🚀 總結

這次改善成功地：
1. **簡化了表單結構**：移除不必要的備註欄位
2. **智能化了時長設定**：根據輔導類型自動決定時長
3. **改善了用戶體驗**：更清楚的提醒和更直觀的操作
4. **提高了資料準確性**：確保時長設定符合業務規則

整個功能現在更加符合實際業務需求，提供了更好的用戶體驗和更準確的資料處理。
