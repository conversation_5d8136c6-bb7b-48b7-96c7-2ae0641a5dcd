# 綠界金流 CheckMacValue 驗證功能說明

## 概述

CheckMacValue 是綠界金流的重要安全機制，用於驗證通知的真實性，防止惡意攻擊和偽造通知。本系統已完整實現綠界官方標準的 CheckMacValue 驗證功能。

## 實現的功能

### 1. CheckMacValue 計算函數

```go
func calculateEcpayCheckMacValue(params map[string]string, hashKey, hashIV string) string
```

**功能說明**：
- 按照綠界官方規範計算 CheckMacValue
- 支援所有參數的字母排序
- 正確處理 URL 編碼和大小寫轉換
- 使用 SHA256 加密算法

**計算步驟**：
1. 移除 CheckMacValue 參數
2. 將所有參數按字母順序排序
3. 組合成 key=value&key=value 格式
4. 前後加上 HashKey 和 HashIV
5. 進行 URL 編碼
6. 轉換為小寫
7. SHA256 加密
8. 轉換為大寫

### 2. CheckMacValue 驗證函數

```go
func verifyEcpayCheckMacValue(params map[string]string, hashKey, hashIV string) bool
```

**功能說明**：
- 驗證接收到的 CheckMacValue 是否正確
- 比對計算結果與接收到的值
- 返回驗證結果 (true/false)

### 3. 通知處理中的驗證

在 `NotifyEcpayOrder` 函數中：
- 收集所有 POST 參數
- 自動驗證 CheckMacValue
- 驗證失敗時拒絕處理並返回錯誤
- 只有驗證成功才繼續處理交易
- **注意**: 通知處理不需要 SDK client，因為這是被動接收綠界通知

## 安全特性

### 1. 防偽造攻擊
- 只有擁有正確 HashKey 和 HashIV 的一方才能生成正確的 CheckMacValue
- 任何參數被篡改都會導致驗證失敗

### 2. 完整性檢查
- 驗證所有參數的完整性
- 確保通知確實來自綠界而非第三方

### 3. 重放攻擊防護
- 結合訂單編號等唯一參數
- 防止重複處理相同通知

## 測試驗證

### 測試用例

已創建測試用例驗證 CheckMacValue 計算的正確性：

```go
func TestEcpayCheckMacValue(t *testing.T)
```

**測試內容**：
- 使用綠界官方文檔中的範例資料
- 驗證計算結果與預期值相符
- 確保實現符合官方規範

### 測試資料

使用綠界官方文檔中的範例：
- 商戶ID: 3002607
- 交易編號: ECPay1738978034
- 預期 CheckMacValue: C66199663DD43BF01058218601BEE874315E5FF57A1FE112A9114AC3701947BA

## 使用方式

### 1. 自動驗證

在接收綠界通知時，系統會自動：
1. 提取所有 POST 參數
2. 根據商戶設定取得 HashKey 和 HashIV
3. 計算 CheckMacValue
4. 與接收到的值比對
5. 驗證失敗時拒絕處理

### 2. 錯誤處理

驗證失敗時：
- 返回 HTTP 400 錯誤
- 提供明確的錯誤訊息
- 記錄相關資訊供排查

## 配置說明

### 測試環境

```go
const (
    EcpayTestMerchantID = "3366242"
    EcpayTestHashKey    = "aHk46nU5rBAIS7kp"
    EcpayTestHashIV     = "hpOOGBqq2jracs3x"
)
```

### 正式環境

從資料庫 `payment_options` 表中讀取：
- merchant_id: 商戶編號
- hash_key: HashKey
- hash_iv: HashIV

## 注意事項

### 1. 安全性
- HashKey 和 HashIV 必須保密
- 定期更換密鑰
- 使用 HTTPS 傳輸

### 2. 相容性
- 嚴格按照綠界官方規範實現
- 支援所有綠界支援的參數
- 正確處理中文字符編碼

### 3. 效能
- 驗證過程輕量快速
- 不影響正常交易流程
- 適合高併發環境

## 錯誤排查

### 常見問題

1. **CheckMacValue 驗證失敗**
   - 檢查 HashKey 和 HashIV 是否正確
   - 確認參數完整性
   - 檢查字符編碼

2. **參數缺失**
   - 確認所有必要參數都有傳送
   - 檢查參數名稱是否正確

3. **編碼問題**
   - 確保 URL 編碼正確
   - 檢查中文字符處理

### 除錯方法

1. 記錄接收到的所有參數
2. 記錄計算過程中的中間結果
3. 與綠界官方範例比對
4. 使用測試環境驗證

## 結論

本系統已完整實現綠界金流的 CheckMacValue 驗證機制，符合官方安全規範，能有效防止偽造攻擊，確保交易安全。驗證功能已通過測試，可以安全部署到生產環境使用。
