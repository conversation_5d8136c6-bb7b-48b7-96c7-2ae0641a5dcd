# 移除輔導卡道具功能測試指南

## 修改內容

已移除課程輔導預約中的道具卡（輔導卡）使用機制，讓學員可以直接申請課程輔導，無需消耗道具卡。

### 主要變更

#### 1. 前端修改
**移除道具卡選擇流程**：
- 移除 `getCounselGoods()` 方法
- 移除道具卡選擇對話框
- 移除 `counselGoods` 資料屬性
- 簡化 `applyCounsel()` 方法

#### 2. 後端 API 修改
**ApplyCounsel API 簡化**：
- 移除 `mem_goods_id` 參數要求
- 移除道具卡使用和記錄邏輯
- 移除道具卡狀態更新
- 移除 `CreateGoodsUsedRecord` 調用

#### 3. 模型邏輯增強
**CounselAppointment.Create 方法**：
- 新增不使用道具卡的課程輔導預約邏輯
- 保持向後相容性（仍支援道具卡方式）
- 設定預設輔導時間為 60 分鐘

## 修改詳情

### 1. 前端簡化
**修改前的複雜流程**：
```javascript
async applyCounsel(pro) {
    // 1. 獲取可用的輔導卡
    let res = await this.getCounselGoods(pro)
    
    // 2. 檢查是否有可用輔導卡
    if (this.counselGoods.length == 0) {
        msgError("沒有可以使用的輔導卡！");
        return;
    }
    
    // 3. 讓使用者選擇輔導卡
    res = await msgConfirmSelect(options, {
        title: '請選擇使用的輔導卡',
    })
    
    // 4. 確認並提交
    msgConfirm('確認申請', () => {
        axiosRequest().post(`/api/members/counsels/${pro.mem_pro_id}`, { 
            mem_goods_id: memGoodsID 
        })
    })
}
```

**修改後的簡化流程**：
```javascript
applyCounsel(pro) {
    // 直接確認並提交
    msgConfirm('確認申請', () => {
        axiosRequest().post(`/api/members/counsels/${pro.mem_pro_id}`)
    })
}
```

### 2. 後端 API 簡化
**修改前的複雜邏輯**：
```go
func ApplyCounsel(c *gin.Context) {
    memGoodsID := c.PostForm("mem_goods_id")
    
    // 創建預約並使用道具卡
    appoint.Create(conn, &CounselAppointmentCreateParams{
        MemGoodsID: StrToUint(memGoodsID),
    })
    
    // 更新道具卡狀態
    memGoods := MemberGoods{
        ID: StrToUint(memGoodsID),
        UsedAt: null.TimeFrom(time.Now()),
        Status: "Y",
    }
    conn.Updates(&memGoods)
    
    // 創建道具使用記錄
    CreateGoodsUsedRecord(conn, info.ID, &memGoods, "Y", "")
}
```

**修改後的簡化邏輯**：
```go
func ApplyCounsel(c *gin.Context) {
    // 直接創建預約，不使用道具卡
    appoint.Create(conn, &CounselAppointmentCreateParams{
        MemberID:   info.ID,
        ProductID:  null.IntFrom(int64(memPro.ProductID)),
        MemProID:   null.IntFrom(int64(StrToUint(memProID))),
        MemGoodsID: 0, // 不使用道具卡
    })
}
```

### 3. 模型邏輯增強
**新增的課程輔導預約邏輯**：
```go
} else if param.MemProID.Valid {
    // 新的課程輔導預約方式（不使用道具卡）
    appoint.AppointmentType = AppointmentTypeCounsel
    if err := conn.Model(&MemberProduct{ID: uint(param.MemProID.Int64)}).Select("pro_name").
        Joins("JOIN products ON member_products.product_id = products.id").
        Scan(&proName).Error; err != nil {
        return "查無課程資料", err
    }
    
    // 設定預設輔導時間（60分鐘）
    counselTime = 60
    appoint.Title = fmt.Sprintf("《課程輔導》<BR>%s‧%d分鐘", proName, counselTime)
}
```

## 測試步驟

### 第一步：前端功能測試

#### 1.1 課程輔導預約流程
1. 訪問 `/member/counsel`
2. 點擊「課程輔導預約」按鈕
3. 選擇一個已購買的課程
4. 點擊「申請」按鈕

**預期結果**：
- 不會出現輔導卡選擇對話框
- 直接顯示確認對話框
- 確認訊息為：「請確認是否要申請《課程名稱》課後輔導？」

#### 1.2 申請提交測試
1. 在確認對話框中點擊確認
2. 等待請求完成

**預期結果**：
- 顯示載入提示
- 成功後顯示：「申請成功，後續將安排輔導時間」
- 預約列表自動更新
- 新的預約記錄出現

#### 1.3 錯誤處理測試
1. 嘗試申請未完成作業的課程
2. 嘗試申請非本人的課程

**預期結果**：
- 顯示適當的錯誤訊息
- 不會創建預約記錄

### 第二步：後端 API 測試

#### 2.1 API 請求格式驗證
**請求測試**：
```bash
curl -X POST "/api/members/counsels/123" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

**預期結果**：
- 不需要 `mem_goods_id` 參數
- 請求成功返回 200 狀態碼
- 回應訊息：`{"msg": "申請成功"}`

#### 2.2 資料庫記錄驗證
```sql
-- 檢查新創建的預約記錄
SELECT id, member_id, mem_pro_id, mem_goods_id, title, duration, appointment_type
FROM counsel_appointments 
WHERE counsel_type_id IS NULL 
AND mem_goods_id IS NULL
ORDER BY id DESC LIMIT 5;
```

**預期結果**：
- `mem_goods_id` 為 NULL
- `counsel_type_id` 為 NULL
- `appointment_type` 為課程輔導類型
- `title` 格式：「《課程輔導》<BR>課程名稱‧60分鐘」
- `duration` 為 60

### 第三步：向後相容性測試

#### 3.1 舊的道具卡方式測試
如果系統中仍有使用道具卡的需求，測試舊方式是否仍然有效：

```bash
curl -X POST "/api/members/counsels/123" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "mem_goods_id=456"
```

**預期結果**：
- 舊方式仍然有效
- 道具卡狀態正確更新
- 創建相應的使用記錄

#### 3.2 直接預約方式測試
測試新的直接預約輔導功能是否不受影響：

```bash
curl -X POST "/api/members/counsels/direct" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"counsel_type_id": 1, "requested_duration": 60}'
```

**預期結果**：
- 直接預約功能正常
- 不使用道具卡
- 正確設定 `counsel_type_id`

### 第四步：資料一致性測試

#### 4.1 預約類型區分
```sql
-- 檢查三種預約類型的資料特徵
SELECT 
    CASE 
        WHEN counsel_type_id IS NOT NULL THEN '直接預約輔導'
        WHEN mem_goods_id IS NOT NULL THEN '道具卡課程輔導'
        ELSE '免費課程輔導'
    END as appointment_type,
    COUNT(*) as count
FROM counsel_appointments 
GROUP BY 
    CASE 
        WHEN counsel_type_id IS NOT NULL THEN '直接預約輔導'
        WHEN mem_goods_id IS NOT NULL THEN '道具卡課程輔導'
        ELSE '免費課程輔導'
    END;
```

#### 4.2 標題格式檢查
```sql
-- 檢查不同類型預約的標題格式
SELECT title, counsel_type_id, mem_goods_id, mem_pro_id
FROM counsel_appointments 
WHERE created_at >= CURDATE()
ORDER BY id DESC;
```

**預期結果**：
- 直接預約輔導：「《輔導項目名稱》<BR>課程名稱‧時長分鐘」
- 免費課程輔導：「《課程輔導》<BR>課程名稱‧60分鐘」
- 道具卡課程輔導：「《1V1諮詢》<BR>課程名稱‧時長分鐘」

### 第五步：使用者體驗測試

#### 5.1 操作簡化驗證
1. 比較修改前後的操作步驟
2. 測試使用者的理解程度

**預期結果**：
- 操作步驟從 4 步減少到 2 步
- 不再需要理解道具卡概念
- 申請流程更直觀

#### 5.2 錯誤訊息測試
1. 測試各種錯誤情況
2. 確認錯誤訊息清楚易懂

**預期結果**：
- 錯誤訊息不再提及道具卡
- 提示訊息更加直接
- 使用者能快速理解問題

### 第六步：效能測試

#### 6.1 請求效能
1. 測試 API 回應時間
2. 比較修改前後的效能

**預期結果**：
- API 回應時間減少（移除道具卡查詢和更新）
- 資料庫操作減少
- 整體效能提升

#### 6.2 併發測試
1. 同時提交多個課程輔導申請
2. 檢查資料一致性

**預期結果**：
- 併發申請正常處理
- 無資料衝突
- 每個申請都正確記錄

## 成功指標

- [ ] 課程輔導預約不再需要道具卡
- [ ] 前端流程簡化為 2 步操作
- [ ] 後端 API 移除道具卡相關邏輯
- [ ] 新預約記錄正確創建
- [ ] 向後相容性保持良好
- [ ] 直接預約輔導功能不受影響
- [ ] 資料庫記錄格式正確
- [ ] 使用者體驗明顯改善
- [ ] 系統效能有所提升
- [ ] 錯誤處理機制完善

## 業務影響

### 1. 正面影響
- **降低使用門檻**：學員不需要先獲得道具卡
- **簡化操作流程**：減少複雜的選擇步驟
- **提升申請率**：更容易申請課程輔導
- **減少客服負擔**：減少道具卡相關問題

### 2. 注意事項
- **成本控制**：需要其他方式控制輔導申請頻率
- **資源管理**：可能需要更好的時間安排機制
- **統計分析**：需要調整相關報表邏輯

### 3. 後續優化建議
- 添加申請頻率限制機制
- 完善輔導時間安排系統
- 優化後台管理介面
- 增加使用統計功能

這個修改大幅簡化了課程輔導預約流程，提升了使用者體驗，同時保持了系統的穩定性和相容性。
