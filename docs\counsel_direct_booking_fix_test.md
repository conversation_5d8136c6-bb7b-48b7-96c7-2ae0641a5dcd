# 直接預約輔導功能修復測試指南

## 修改內容

已修復 `submitDirectBooking` 功能的錯誤，移除了不必要的時間相關欄位，簡化了 API 請求格式，讓直接預約輔導功能可以正常運作。

### 主要修復

#### 1. 前端請求簡化
**修改前的錯誤請求**：
```javascript
const bookingData = {
    counsel_type_id: counselType.id,
    requested_duration: counselType.default_duration || 60,
    mem_pro_id: 0 // 不關聯特定課程
};
```

**修改後的正確請求**：
```javascript
const bookingData = {
    counsel_type_id: counselType.id
};
```

#### 2. 後端 API 簡化
**修改前的複雜邏輯**：
- 需要 `appointment_date`（必填）
- 需要 `appointment_time`（必填）
- 需要 `requested_duration`（必填）
- 複雜的時間驗證和衝突檢查

**修改後的簡化邏輯**：
- 只需要 `counsel_type_id`（必填）
- `mem_pro_id` 可選（課程關聯）
- 移除所有時間相關的驗證
- 使用輔導項目的預設時長

#### 3. 資料庫記錄特徵
**直接預約輔導記錄**：
```sql
counsel_type_id: 有值（輔導項目 ID）
mem_goods_id: NULL（不使用道具卡）
appointment_date: NULL（待後續安排）
appointment_time: NULL（待後續安排）
requested_duration: 有值（使用預設時長）
```

## API 修改詳情

### 1. 請求結構簡化
**ApplyCounselDirect API**：
```go
// 修改前
var request struct {
    CounselTypeID     uint   `json:"counsel_type_id" binding:"required"`
    AppointmentDate   string `json:"appointment_date" binding:"required"`
    AppointmentTime   string `json:"appointment_time" binding:"required"`
    RequestedDuration int    `json:"requested_duration" binding:"required"`
    MemProID          uint   `json:"mem_pro_id"`
}

// 修改後
var request struct {
    CounselTypeID uint `json:"counsel_type_id" binding:"required"`
    MemProID      uint `json:"mem_pro_id"` // 可選
}
```

### 2. 處理邏輯簡化
**移除的複雜邏輯**：
- 日期時間解析和驗證
- 預約時間範圍檢查
- 時間衝突檢查
- 可預約日期驗證
- 作業完成檢查（針對課程關聯）

**保留的核心邏輯**：
- 輔導項目存在性檢查
- 課程關聯驗證（如果提供）
- 預約記錄創建

### 3. 創建參數調整
```go
// 修改前
appoint.Create(conn, &CounselAppointmentCreateParams{
    MemberID:          info.ID,
    ProductID:         productID,
    MemProID:          memProIDNull,
    MemGoodsID:        0,
    CounselTypeID:     request.CounselTypeID,
    AppointmentDate:   appointmentDate,
    AppointmentTime:   appointmentDateTime,
    RequestedDuration: request.RequestedDuration,
})

// 修改後
appoint.Create(conn, &CounselAppointmentCreateParams{
    MemberID:          info.ID,
    ProductID:         productID,
    MemProID:          memProIDNull,
    MemGoodsID:        0,
    CounselTypeID:     request.CounselTypeID,
    RequestedDuration: counselType.DefaultDuration,
})
```

## 測試步驟

### 第一步：前端功能測試

#### 1.1 直接預約輔導選項顯示
1. 訪問 `/member/counsel`
2. 點擊「直接預約輔導」按鈕
3. 檢查選項表格是否正確顯示

**預期結果**：
- Fancybox 正常彈出
- 表格顯示所有可用的輔導項目
- 每個項目都有「申請」按鈕

#### 1.2 輔導項目申請測試
1. 在選項表格中選擇一個輔導項目
2. 點擊對應的「申請」按鈕
3. 觀察請求過程

**預期結果**：
- 顯示載入提示
- 不會出現「資料錯誤」提示
- 請求成功完成

#### 1.3 成功回饋測試
1. 申請成功後檢查回饋
2. 確認頁面狀態更新

**預期結果**：
- 顯示「預約申請成功」訊息
- 選項表格自動關閉
- 預約列表重新載入
- 新的預約記錄出現

### 第二步：API 請求驗證

#### 2.1 網路請求檢查
使用瀏覽器開發者工具檢查請求：

**請求 URL**：`POST /api/members/counsels/direct`
**請求標頭**：`Content-Type: application/json`
**請求體**：
```json
{
    "counsel_type_id": 1
}
```

**預期回應**：
```json
{
    "msg": "預約申請成功"
}
```

#### 2.2 錯誤情況測試
```bash
# 測試無效的輔導項目 ID
curl -X POST "/api/members/counsels/direct" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"counsel_type_id": 99999}'
```

**預期回應**：
```json
{
    "msg": "查無輔導項目"
}
```

#### 2.3 缺少必要欄位測試
```bash
# 測試缺少 counsel_type_id
curl -X POST "/api/members/counsels/direct" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{}'
```

**預期回應**：
```json
{
    "msg": "資料格式錯誤"
}
```

### 第三步：資料庫記錄驗證

#### 3.1 預約記錄檢查
```sql
-- 檢查新創建的直接預約記錄
SELECT id, member_id, counsel_type_id, mem_goods_id, 
       appointment_date, appointment_time, requested_duration,
       title, created_at
FROM counsel_appointments 
WHERE counsel_type_id IS NOT NULL 
AND mem_goods_id IS NULL
ORDER BY id DESC LIMIT 5;
```

**預期結果**：
- `counsel_type_id` 有正確的輔導項目 ID
- `mem_goods_id` 為 NULL
- `appointment_date` 為 NULL
- `appointment_time` 為 NULL
- `requested_duration` 為輔導項目的預設時長
- `title` 格式正確

#### 3.2 標題格式檢查
**預期標題格式**：
- 無課程關聯：「《輔導項目名稱》<BR>預設時長分鐘」
- 有課程關聯：「《輔導項目名稱》<BR>課程名稱‧預設時長分鐘」

### 第四步：與其他預約類型的區別

#### 4.1 三種預約類型比較
```sql
-- 檢查三種預約類型的資料特徵
SELECT 
    CASE 
        WHEN counsel_type_id IS NOT NULL THEN '直接預約輔導'
        WHEN mem_goods_id IS NOT NULL THEN '道具卡課程輔導'
        ELSE '免費課程輔導'
    END as appointment_type,
    counsel_type_id,
    mem_goods_id,
    mem_pro_id,
    appointment_date,
    appointment_time,
    COUNT(*) as count
FROM counsel_appointments 
WHERE created_at >= CURDATE()
GROUP BY appointment_type, counsel_type_id, mem_goods_id, mem_pro_id, appointment_date, appointment_time;
```

#### 4.2 功能獨立性測試
1. 測試課程輔導預約功能是否正常
2. 測試直接預約輔導功能是否正常
3. 確認兩者不會互相影響

**預期結果**：
- 兩種功能完全獨立
- 資料格式正確區分
- 後台管理可以分別處理

### 第五步：錯誤處理測試

#### 5.1 前端錯誤處理
1. 測試輔導項目資料異常情況
2. 測試網路連線問題
3. 測試 API 回應錯誤

**預期結果**：
- 適當的錯誤訊息顯示
- 不會出現 JavaScript 錯誤
- 使用者能理解問題所在

#### 5.2 後端錯誤處理
1. 測試無效的輔導項目 ID
2. 測試權限問題
3. 測試資料庫連線問題

**預期結果**：
- 正確的 HTTP 狀態碼
- 清楚的錯誤訊息
- 適當的事務回滾

### 第六步：效能測試

#### 6.1 請求回應時間
1. 測試 API 回應速度
2. 比較修改前後的效能

**預期結果**：
- 回應時間明顯減少（移除複雜驗證）
- 資料庫查詢次數減少
- 整體效能提升

#### 6.2 併發測試
1. 同時提交多個直接預約申請
2. 檢查資料一致性

**預期結果**：
- 併發申請正常處理
- 無資料衝突
- 每個申請都正確記錄

## 成功指標

- [ ] 直接預約輔導申請不再出現「資料錯誤」
- [ ] API 請求格式正確簡化
- [ ] 後端邏輯移除不必要的時間驗證
- [ ] 預約記錄正確創建到資料庫
- [ ] 前端回饋訊息正確顯示
- [ ] 與其他預約類型功能獨立
- [ ] 錯誤處理機制完善
- [ ] 系統效能有所提升
- [ ] 資料庫記錄格式正確
- [ ] 後台管理功能不受影響

## 修復效果

### 1. 功能恢復
- **直接預約輔導**：現在可以正常申請
- **簡化流程**：只需選擇輔導項目即可
- **即時回饋**：申請成功後立即顯示結果

### 2. 系統穩定性
- **移除複雜邏輯**：減少出錯可能性
- **簡化驗證**：只保留必要的檢查
- **提升效能**：減少不必要的計算

### 3. 使用者體驗
- **操作簡單**：一鍵申請輔導
- **回饋清楚**：明確的成功/失敗訊息
- **流程順暢**：無複雜的表單填寫

### 4. 開發維護
- **程式碼簡化**：更容易理解和維護
- **邏輯清晰**：職責分離更明確
- **擴展性好**：便於未來功能擴展

這個修復成功解決了直接預約輔導功能的錯誤，大幅簡化了申請流程，提升了系統的穩定性和使用者體驗。
