# 輔導時間管理修復測試指南

## 修復內容

### 1. 日期格式錯誤修復
**問題**: 前端傳送的日期格式 "2025-08-19" 與後端期望的時間格式不匹配
**修復**: 
- 修改後端 API 使用專門的請求結構
- 使用 `time.Parse("2006-01-02", requestData.Date)` 正確解析日期格式
- 分別處理新增和更新的日期解析邏輯

### 2. 時段重疊檢測功能
**新增功能**:
- 即時檢測時間段重疊
- 視覺化提醒（邊框高亮、警告文字）
- 提交前驗證防止保存重疊時段

## 測試步驟

### 第一步：日期格式測試

#### 1.1 新增可預約時間
1. 訪問 `/admin/counsel/available-times/reg`
2. 選擇日期（如 2025-08-19）
3. 設定時間段
4. 提交表單
5. **預期結果**: 成功保存，不再出現日期格式錯誤

#### 1.2 編輯可預約時間
1. 編輯現有的可預約時間
2. 修改日期
3. 提交表單
4. **預期結果**: 成功更新，不再出現日期格式錯誤

### 第二步：時段重疊檢測測試

#### 2.1 基本重疊檢測
1. 新增可預約時間
2. 添加第一個時間段：09:00-12:00
3. 添加第二個時間段：11:00-14:00（與第一個重疊）
4. **預期結果**:
   - 時間輸入框出現橙色邊框警告
   - 顯示「時段重疊」紅色文字提醒
   - 時間段區域下方顯示整體警告訊息

#### 2.2 邊界情況測試
1. 測試相鄰時間段（不重疊）：
   - 第一段：09:00-12:00
   - 第二段：12:00-15:00
   - **預期結果**: 不應顯示重疊警告

2. 測試完全包含的時間段：
   - 第一段：09:00-17:00
   - 第二段：10:00-12:00
   - **預期結果**: 顯示重疊警告

3. 測試部分重疊：
   - 第一段：09:00-13:00
   - 第二段：12:00-16:00
   - **預期結果**: 顯示重疊警告

#### 2.3 多個時段重疊測試
1. 添加三個時間段：
   - 第一段：09:00-12:00
   - 第二段：11:00-14:00
   - 第三段：13:00-16:00
2. **預期結果**: 
   - 第一段和第二段顯示重疊警告
   - 第二段和第三段顯示重疊警告
   - 整體警告訊息顯示

#### 2.4 即時檢測測試
1. 添加兩個不重疊的時間段
2. 修改其中一個時間段使其與另一個重疊
3. **預期結果**: 
   - 修改時即時顯示重疊警告
   - 不需要失去焦點或提交表單

#### 2.5 提交驗證測試
1. 設定重疊的時間段
2. 嘗試提交表單
3. **預期結果**: 
   - 顯示錯誤訊息：「時間段不能重疊，請調整時間設定」
   - 表單不會提交
   - 重疊的時間段仍然高亮顯示

### 第三步：視覺效果測試

#### 3.1 邊框高亮測試
1. 創建重疊時間段
2. **檢查項目**:
   - 重疊時段的開始時間輸入框有橙色邊框
   - 重疊時段的結束時間輸入框有橙色邊框
   - 邊框有淡橙色陰影效果

#### 3.2 文字提醒測試
1. 創建重疊時間段
2. **檢查項目**:
   - 每個重疊時段旁邊顯示紅色「時段重疊」文字
   - 文字前有警告圖標
   - 整體警告區域有黃色背景

#### 3.3 警告區域測試
1. 創建重疊時間段
2. **檢查項目**:
   - 時間段設定區域下方顯示警告框
   - 警告框有黃色背景和邊框
   - 警告文字清楚易懂

### 第四步：功能整合測試

#### 4.1 與現有預約衝突檢測
1. 在有現有預約的日期編輯可預約時間
2. 設定與預約重疊的時間段
3. **預期結果**: 
   - 同時顯示「與現有預約重疊」和「時段重疊」警告（如果適用）
   - 兩種警告使用不同顏色區分

#### 4.2 修復重疊後的行為
1. 創建重疊時間段
2. 修正重疊問題
3. **預期結果**:
   - 警告立即消失
   - 邊框恢復正常
   - 可以正常提交表單

## API 測試

### 測試新增 API
```bash
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2025-08-19",
    "time_slot_options": [
      {"start": "09:00", "end": "12:00"},
      {"start": "14:00", "end": "17:00"}
    ],
    "status": "Y",
    "note": "測試時間"
  }'
```

### 測試更新 API
```bash
curl -X PATCH "/api/admin/counsel-available-times/1" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2025-08-20",
    "time_slot_options": [
      {"start": "10:00", "end": "13:00"},
      {"start": "15:00", "end": "18:00"}
    ],
    "status": "Y",
    "note": "更新測試"
  }'
```

## 預期結果總結

### 成功指標
- [ ] 日期格式錯誤完全修復
- [ ] 時段重疊即時檢測正常
- [ ] 視覺警告清楚明顯
- [ ] 提交驗證防止錯誤資料
- [ ] 不影響現有功能

### 視覺效果檢查
- [ ] 重疊時段有橙色邊框高亮
- [ ] 顯示紅色「時段重疊」文字
- [ ] 整體警告區域有黃色背景
- [ ] 警告圖標正確顯示

### 功能檢查
- [ ] 即時檢測重疊（不需提交）
- [ ] 提交時驗證重疊
- [ ] 修復重疊後警告消失
- [ ] 與現有預約衝突檢測並存

## 常見問題排除

### 問題 1：日期格式仍然錯誤
**檢查**: 確認前端傳送的日期格式是否為 "YYYY-MM-DD"
**解決**: 檢查前端日期輸入的 v-model 綁定

### 問題 2：重疊檢測不靈敏
**檢查**: 確認時間輸入後是否觸發 @change 事件
**解決**: 檢查 checkTimeSlotOverlap 方法是否正確調用

### 問題 3：視覺效果不顯示
**檢查**: 確認 CSS 樣式是否正確載入
**解決**: 檢查瀏覽器開發者工具中的樣式應用情況
