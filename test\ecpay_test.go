package test

import (
	"crypto/sha256"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"testing"
	"time"

	ecpay "github.com/Laysi/go-ecpay-sdk"
	"github.com/stretchr/testify/assert"
)

func TestEcpayStageClient(t *testing.T) {
	// 測試綠界金流測試環境客戶端
	client := ecpay.NewStageClient(
		ecpay.WithReturnURL("https://example.com/notify"),
	)

	// 測試創建訂單
	html, err := client.CreateOrder("TEST123", time.Now(), 1000, "測試訂單", []string{"測試商品"}).
		SetAllPayment().
		GenerateRequestHtml()

	assert.NoError(t, err)
	assert.NotEmpty(t, html)
	assert.Contains(t, html, "form")
}

func TestEcpayProductionClient(t *testing.T) {
	// 測試綠界金流正式環境客戶端
	client := ecpay.NewClient("TEST_MERCHANT", "TEST_KEY", "TEST_IV", "https://example.com/notify")

	// 測試創建訂單
	html, err := client.CreateOrder("TEST123", time.Now(), 1000, "測試訂單", []string{"測試商品"}).
		SetAllPayment().
		GenerateRequestHtml()

	assert.NoError(t, err)
	assert.NotEmpty(t, html)
	assert.Contains(t, html, "form")
}

// 計算綠界金流 CheckMacValue (測試用)
func calculateEcpayCheckMacValue(params map[string]string, hashKey, hashIV string) string {
	// 移除 CheckMacValue 參數
	delete(params, "CheckMacValue")

	// 將參數按照字母順序排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 組合參數字串
	var paramStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			paramStr.WriteString("&")
		}
		paramStr.WriteString(k)
		paramStr.WriteString("=")
		paramStr.WriteString(params[k])
	}

	// 加上 HashKey 和 HashIV
	fullStr := fmt.Sprintf("HashKey=%s&%s&HashIV=%s", hashKey, paramStr.String(), hashIV)

	// URL encode
	encoded := url.QueryEscape(fullStr)

	// 轉小寫
	encoded = strings.ToLower(encoded)

	// SHA256 加密
	hash := sha256.Sum256([]byte(encoded))

	// 轉大寫並返回
	return strings.ToUpper(fmt.Sprintf("%x", hash))
}

func TestEcpayCheckMacValue(t *testing.T) {
	// 測試綠界金流 CheckMacValue 計算
	params := map[string]string{
		"MerchantID":           "3002607",
		"MerchantTradeNo":      "ECPay1738978034",
		"PaymentDate":          "2025/02/08 09:32:20",
		"PaymentType":          "Credit_CreditCard",
		"PaymentTypeChargeFee": "1",
		"RtnCode":              "1",
		"RtnMsg":               "交易成功",
		"SimulatePaid":         "0",
		"TradeAmt":             "30",
		"TradeDate":            "2025/02/08 09:27:18",
		"TradeNo":              "2502080927183709",
		"CustomField1":         "",
		"CustomField2":         "",
		"CustomField3":         "",
		"CustomField4":         "",
		"StoreID":              "",
	}

	hashKey := "pwFHCqoQZGmho4w6"
	hashIV := "EkRm7iFT261dpevs"

	expectedCheckMac := "C66199663DD43BF01058218601BEE874315E5FF57A1FE112A9114AC3701947BA"

	calculatedCheckMac := calculateEcpayCheckMacValue(params, hashKey, hashIV)

	assert.Equal(t, expectedCheckMac, calculatedCheckMac, "CheckMacValue 計算結果應該相符")
}
