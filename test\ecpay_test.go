package test

import (
	"testing"
	"time"

	ecpay "github.com/Laysi/go-ecpay-sdk"
	"github.com/stretchr/testify/assert"
)

func TestEcpayStageClient(t *testing.T) {
	// 測試綠界金流測試環境客戶端
	client := ecpay.NewStageClient(
		ecpay.WithReturnURL("https://example.com/notify"),
	)

	// 測試創建訂單
	html, err := client.CreateOrder("TEST123", time.Now(), 1000, "測試訂單", []string{"測試商品"}).
		SetAllPayment().
		GenerateRequestHtml()

	assert.NoError(t, err)
	assert.NotEmpty(t, html)
	assert.Contains(t, html, "form")
}

func TestEcpayProductionClient(t *testing.T) {
	// 測試綠界金流正式環境客戶端
	client := ecpay.NewClient("TEST_MERCHANT", "TEST_KEY", "TEST_IV", "https://example.com/notify")

	// 測試創建訂單
	html, err := client.CreateOrder("TEST123", time.Now(), 1000, "測試訂單", []string{"測試商品"}).
		SetAllPayment().
		GenerateRequestHtml()

	assert.NoError(t, err)
	assert.NotEmpty(t, html)
	assert.Contains(t, html, "form")
}
