BEGIN;

CREATE TABLE `ecpay_orders` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `order_no` varchar(255) NOT NULL UNIQUE,
    `rtn_code` varchar(10) NOT NULL DEFAULT '',
    `rtn_msg` varchar(255) NOT NULL DEFAULT '',
    `merchant_id` varchar(20) NOT NULL DEFAULT '',
    `merchant_trade_no` varchar(255) NOT NULL DEFAULT '',
    `trade_no` varchar(255) NOT NULL DEFAULT '',
    `trade_amt` int unsigned NOT NULL DEFAULT '0',
    `payment_date` varchar(50) NOT NULL DEFAULT '',
    `payment_type` varchar(50) NOT NULL DEFAULT '',
    `payment_type_charge_fee` varchar(20) NOT NULL DEFAULT '',
    `trade_date` varchar(50) NOT NULL DEFAULT '',
    `simulate_paid` varchar(10) NOT NULL DEFAULT '',
    `check_mac_value` varchar(255) NOT NULL DEFAULT '',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_merchant_trade_no` (`merchant_trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
