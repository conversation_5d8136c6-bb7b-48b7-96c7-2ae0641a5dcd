# 會員輔導預約時段選擇功能實現總結

## 🎯 功能目標

在前台會員輔導頁面的預約記錄Tab中，新增可選擇預約時段的功能，讓會員可以為已完成作業的輔導預約選擇具體的預約時段。

## ✅ 功能規則

### 顯示條件
1. **預約尚未被設定時間**：`appointment_date IS NULL`
2. **預約的所有輔導作業皆完成上傳**：所有相關作業都有 `upload_file`

### 操作流程
1. 會員點擊「選擇時段」按鈕
2. 選擇想要申請的日期
3. 系統顯示後台設定的「可預約時段」
4. 選擇輔導時長
5. 送出後檢查是否會和其他輔導時間重疊
6. 如果重疊則跳出提醒，否則完成預約

## 🎨 前端界面實現

### 1. 可選擇預約時段區域

#### 顯示條件
```html
<div class="mb-4" v-if="readyToScheduleAppointments.length > 0">
```

#### 表格結構
```html
<table class="table table-responsive reflow-table reflow-table-w-50 reflow-table-sm">
    <thead>
        <tr>
            <th>輔導名稱</th>
            <th>申請時間</th>
            <th>作業狀態</th>
            <th>操作</th>
        </tr>
    </thead>
    <tbody>
        <tr v-for="appointment in readyToScheduleAppointments">
            <td>
                <div class="fw-bold">${ appointment.title }</div>
                <small class="text-muted">${ appointment.counsel_type_name }</small>
            </td>
            <td>${ formatDate(appointment.apply_at, '/') }</td>
            <td>
                <span class="badge bg-success">
                    <i class="fas fa-check-circle me-1"></i>作業已完成
                </span>
            </td>
            <td>
                <button @click="selectScheduleTime(appointment)" class="btn btn-sm btn-primary">
                    <i class="fas fa-calendar-plus me-1"></i>選擇時段
                </button>
            </td>
        </tr>
    </tbody>
</table>
```

### 2. 選擇時段彈出式表單

#### 表單結構
```html
<div id="schedule-time-form" class="info-fancy fancy_sec p-4" style="display:none;">
    <h3 class="text-center mb-4">
        <i class="fas fa-calendar-check me-2"></i>選擇預約時段
    </h3>
    
    <!-- 輔導資訊顯示 -->
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle me-2"></i>輔導資訊</h5>
        <p><strong>輔導名稱：</strong>${ selectedAppointment.title }</p>
        <p><strong>輔導類型：</strong>${ selectedAppointment.counsel_type_name }</p>
    </div>

    <form @submit.prevent="submitScheduleTime">
        <!-- 選擇日期 -->
        <input type="date" v-model="scheduleForm.date" @change="loadAvailableTimes">
        
        <!-- 可預約時段 -->
        <div v-if="availableTimes.length > 0">
            <div v-for="time in availableTimes">
                <input type="radio" v-model="scheduleForm.time" :value="time">
                <label>${ time }</label>
            </div>
        </div>
        
        <!-- 輔導時長 -->
        <select v-model="scheduleForm.duration">
            <option value="30">30分鐘</option>
            <option value="60">60分鐘</option>
            <option value="90">90分鐘</option>
            <option value="120">120分鐘</option>
        </select>
        
        <!-- 備註 -->
        <textarea v-model="scheduleForm.note" placeholder="特殊需求或說明"></textarea>
        
        <!-- 提交按鈕 -->
        <button type="submit" :disabled="!canSubmitSchedule">確認預約</button>
        <button type="button" @click="cancelScheduleTime">取消</button>
    </form>
</div>
```

## 🔧 Vue.js 資料結構

### Data 定義
```javascript
data() {
    return {
        // 可選擇預約時段相關數據
        readyToScheduleAppointments: [],
        selectedAppointment: null,
        scheduleForm: {
            date: '',
            time: '',
            duration: '',
            note: ''
        },
        availableTimes: [],
        loadingTimes: false,
    }
}
```

### Computed 屬性
```javascript
computed: {
    canSubmitSchedule() {
        return this.scheduleForm.date && 
               this.scheduleForm.time && 
               this.scheduleForm.duration &&
               !this.loadingTimes
    }
}
```

## 📡 JavaScript 方法實現

### 1. 獲取可預約列表
```javascript
getReadyToScheduleAppointments() {
    axiosRequest()
        .get('/api/members/counsels/ready-to-schedule')
        .then(res => {
            this.readyToScheduleAppointments = res.data || []
        })
        .catch(err => {
            console.log(err)
            this.readyToScheduleAppointments = []
        })
}
```

### 2. 選擇預約時段
```javascript
selectScheduleTime(appointment) {
    this.selectedAppointment = appointment
    this.resetScheduleForm()
    
    $.fancybox.open({
        src: '#schedule-time-form',
        type: 'inline',
        opts: {
            toolbar: false,
            smallBtn: true,
            loop: false,
            touch: false,
            clickSlide: false,
            clickOutside: false,
        },
    })
}
```

### 3. 載入可預約時段
```javascript
loadAvailableTimes() {
    if (!this.scheduleForm.date) {
        this.availableTimes = []
        return
    }
    
    this.loadingTimes = true
    this.availableTimes = []
    
    axiosRequest()
        .get(`/api/members/counsels/available-times?date=${this.scheduleForm.date}`)
        .then(res => {
            this.availableTimes = res.data || []
            this.scheduleForm.time = '' // 重置選擇的時間
        })
        .catch(err => {
            console.log(err)
            this.availableTimes = []
            msgError('載入可預約時段失敗')
        })
        .finally(() => {
            this.loadingTimes = false
        })
}
```

### 4. 提交預約時段
```javascript
submitScheduleTime() {
    if (!this.canSubmitSchedule) {
        msgError('請填寫完整的預約資訊')
        return
    }
    
    msgLoading()
    
    const scheduleData = {
        appointment_id: this.selectedAppointment.id,
        appointment_date: this.scheduleForm.date,
        appointment_time: this.scheduleForm.time,
        requested_duration: parseInt(this.scheduleForm.duration),
        note: this.scheduleForm.note
    }
    
    axiosRequest('json')
        .post('/api/members/counsels/schedule', scheduleData)
        .then(res => {
            msgTopSuccess('預約時段設定成功')
            this.cancelScheduleTime()
            this.getData() // 重新載入資料
            this.getReadyToScheduleAppointments() // 重新載入可預約列表
        })
        .catch(err => {
            console.log(err)
            msgError(err.response?.data?.msg || '預約失敗')
        })
}
```

## 🚀 後端 API 實現

### 1. 獲取可選擇預約時段的輔導
**端點**: `GET /api/members/counsels/ready-to-schedule`

**功能**: 查詢滿足條件的輔導預約
- 預約尚未被設定時間 (`appointment_date IS NULL`)
- 預約的所有輔導作業皆完成上傳

```go
func GetReadyToScheduleAppointments(c *gin.Context) {
    // 查詢滿足條件的輔導預約
    if err := conn.Raw(`
        SELECT 
            ca.id,
            ca.title,
            ct.name AS counsel_type_name,
            ca.created_at AS apply_at,
            ca.status
        FROM counsel_appointments ca
        LEFT JOIN counsel_types ct ON ca.counsel_type_id = ct.id
        WHERE ca.member_id = ?
        AND ca.appointment_date IS NULL
        AND ca.status IN ('pending', 'work_failed')
        AND NOT EXISTS (
            SELECT 1 
            FROM member_counsel_works mcw 
            WHERE mcw.counsel_appointment_id = ca.id 
            AND (mcw.upload_file IS NULL OR mcw.upload_file = '')
        )
        ORDER BY ca.created_at DESC
    `, info.ID).Scan(&data).Error; err != nil {
        // 錯誤處理
    }
}
```

### 2. 獲取可預約時段
**端點**: `GET /api/members/counsels/available-times?date=YYYY-MM-DD`

**功能**: 獲取指定日期的可預約時段

```go
func GetAvailableTimesForSchedule(c *gin.Context) {
    date := c.Query("date")
    
    // 使用現有的優先級邏輯獲取有效的可預約時間
    effectiveTime, err := GetEffectiveAvailableTime(conn, dateTime)
    if err != nil {
        c.JSON(http.StatusOK, gin.H{
            "data": []string{}, // 返回空陣列而不是錯誤
        })
        return
    }

    // 提取時間選項
    timeOptions := []string{}
    for _, slot := range effectiveTime.TimeSlotOptions {
        timeOptions = append(timeOptions, slot.Start)
    }

    c.JSON(http.StatusOK, gin.H{
        "data": timeOptions,
    })
}
```

### 3. 提交預約時段
**端點**: `POST /api/members/counsels/schedule`

**功能**: 設定輔導預約的具體時段

```go
func ScheduleAppointmentTime(c *gin.Context) {
    var requestData struct {
        AppointmentID     uint   `json:"appointment_id" binding:"required"`
        AppointmentDate   string `json:"appointment_date" binding:"required"`
        AppointmentTime   string `json:"appointment_time" binding:"required"`
        RequestedDuration int    `json:"requested_duration" binding:"required"`
        Note              string `json:"note"`
    }

    // 檢查預約是否存在且屬於該會員
    // 檢查是否已設定時間
    // 檢查時間衝突
    // 更新預約時間
}
```

## 🔒 安全性檢查

### 1. 權限驗證
- 檢查預約是否屬於當前會員
- 驗證預約狀態是否允許設定時間

### 2. 資料驗證
- 驗證日期格式
- 檢查時間是否在可預約範圍內
- 驗證輔導時長是否合理

### 3. 衝突檢查
```go
// 檢查時間衝突
conflictCount := int64(0)
if err := conn.Model(&CounselAppointment{}).
    Where("member_id = ?", info.ID).
    Where("appointment_date = ?", requestData.AppointmentDate).
    Where("appointment_time = ?", requestData.AppointmentTime).
    Where("id != ?", requestData.AppointmentID).
    Count(&conflictCount).Error; err != nil {
    // 錯誤處理
}

if conflictCount > 0 {
    c.JSON(http.StatusBadRequest, gin.H{
        "msg": "此時段已有其他輔導預約，請選擇其他時間",
    })
    return
}
```

## 📋 路由設定

### 新增的 API 端點
```go
counsel := member.Group("/counsels")
counsel.GET("/ready-to-schedule", GetReadyToScheduleAppointments).
    GET("/available-times", GetAvailableTimesForSchedule).
    POST("/schedule", ScheduleAppointmentTime)
```

## 🎯 使用者體驗設計

### 1. 視覺設計
- **清楚的狀態指示**: 使用顏色和圖示表示作業完成狀態
- **直觀的操作流程**: 從選擇預約到完成設定的線性流程
- **即時反饋**: 載入狀態、成功提示、錯誤警告

### 2. 互動設計
- **條件顯示**: 只有滿足條件的預約才會顯示
- **動態載入**: 選擇日期後動態載入可預約時段
- **表單驗證**: 確保所有必填欄位都已填寫

### 3. 錯誤處理
- **友善提示**: 清楚的錯誤訊息和操作指引
- **衝突檢查**: 預防時間重疊的預約
- **狀態回滾**: 操作失敗時保持原狀態

## 🚀 功能特色

### 1. 智能篩選
- 自動篩選已完成作業的預約
- 排除已設定時間的預約
- 只顯示符合條件的預約

### 2. 整合現有系統
- 使用現有的可預約時間設定
- 整合現有的時間衝突檢查
- 保持與後台管理的一致性

### 3. 完整的操作流程
- 從條件檢查到時段選擇的完整流程
- 包含所有必要的驗證和檢查
- 提供完整的用戶反饋

這個功能為會員提供了便利的預約時段選擇機制，讓已完成作業的輔導預約可以順利進入下一階段的時間安排，提升了整體的用戶體驗和系統效率。
