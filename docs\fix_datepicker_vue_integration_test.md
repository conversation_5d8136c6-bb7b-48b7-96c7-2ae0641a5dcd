# Datepicker 與 Vue 整合問題修復測試指南

## 問題描述

由於將日期輸入改為使用 datepicker 而不是 v-model，導致：
1. Vue 的 watch 機制無法監聽到日期變化
2. `checkExistingAppointments` 方法無法自動觸發
3. datepicker 與 Vue 的響應式系統脫節

## 解決方案

### 多重監聽策略
實施了5種不同的監聽方法，確保在各種情況下都能捕獲日期變化：

1. **change 事件監聽**：監聽 input 的 change 事件
2. **input 事件監聽**：即時監聽輸入變化
3. **MutationObserver**：監聽 DOM 屬性變化
4. **定期檢查**：每秒檢查一次值的變化
5. **datepicker 回調**：設定 jQuery UI datepicker 的 onSelect 回調

### 備用手動觸發
提供手動同步按鈕，讓使用者可以主動觸發檢查。

## 測試步驟

### 第一步：自動觸發測試

#### 1.1 基本日期選擇測試
1. 訪問 `/admin/counsel/available-times/reg`
2. 點擊日期輸入框
3. 從 datepicker 選擇一個日期
4. **預期結果**：
   - 日期自動填入輸入框
   - 自動觸發 `checkExistingAppointments`
   - 如果該日期有預約，顯示「已有預約」區域

#### 1.2 手動輸入日期測試
1. 直接在日期輸入框中輸入日期（如 2025-08-19）
2. 點擊其他地方或按 Tab 鍵
3. **預期結果**：
   - 自動觸發檢查
   - 載入該日期的預約資訊

#### 1.3 日期變更測試
1. 選擇第一個日期
2. 再選擇另一個日期
3. **預期結果**：
   - 每次變更都觸發檢查
   - 預約資訊正確更新

### 第二步：手動觸發測試

#### 2.1 手動同步按鈕測試
1. 選擇一個日期
2. 點擊「檢查該日期預約」按鈕
3. **預期結果**：
   - 強制同步日期值
   - 觸發預約檢查
   - 顯示該日期的預約資訊

#### 2.2 提交前同步測試
1. 選擇日期並設定時間段
2. 點擊提交按鈕
3. **預期結果**：
   - 提交前自動同步日期值
   - 驗證通過後正常提交

### 第三步：不同 Datepicker 類型測試

#### 3.1 jQuery UI Datepicker 測試
如果使用 jQuery UI datepicker：
1. 檢查是否有 `hasDatepicker` 類別
2. 測試 onSelect 回調是否正常
3. **預期結果**：選擇日期時立即觸發檢查

#### 3.2 其他 Datepicker 插件測試
如果使用其他 datepicker 插件：
1. 測試 change 和 input 事件監聽
2. 測試 MutationObserver 監聽
3. **預期結果**：至少有一種方法能捕獲變化

### 第四步：邊界情況測試

#### 4.1 快速連續變更測試
1. 快速連續選擇多個不同日期
2. **預期結果**：
   - 不會出現重複請求
   - 最終顯示最後選擇日期的預約

#### 4.2 無效日期測試
1. 輸入無效的日期格式
2. **預期結果**：
   - 不觸發預約檢查
   - 提交時顯示錯誤訊息

#### 4.3 空日期測試
1. 清空日期輸入框
2. **預期結果**：
   - 不觸發預約檢查
   - 隱藏預約資訊區域

### 第五步：效能測試

#### 5.1 定期檢查效能測試
1. 開啟瀏覽器開發者工具
2. 觀察 Console 和 Network 標籤
3. **檢查項目**：
   - 定期檢查不會造成過多的 API 請求
   - 只有在日期真正變化時才發送請求

#### 5.2 記憶體洩漏測試
1. 多次進入和離開頁面
2. 檢查事件監聽器是否正確清理
3. **預期結果**：沒有記憶體洩漏

## 瀏覽器開發者工具檢查

### Console 檢查
開啟 Console 並執行以下命令來測試：

```javascript
// 檢查 Vue 實例
const app = document.querySelector('#app').__vue__

// 手動觸發同步
app.syncDateAndCheck()

// 檢查當前日期值
console.log('Vue data.date:', app.data.date)
console.log('Input value:', $('[name="date"]').val())

// 手動設定日期並觸發檢查
$('[name="date"]').val('2025-08-19').trigger('change')
```

### Network 檢查
1. 監控 `/api/admin/counsels` 請求
2. 確認請求參數正確
3. 檢查回應資料格式

## 故障排除

### 問題 1：自動觸發不工作
**檢查步驟**：
1. 確認 datepicker 是否正確初始化
2. 檢查 Console 是否有 JavaScript 錯誤
3. 測試手動觸發按鈕是否工作

**解決方案**：
- 使用手動觸發按鈕作為備用
- 檢查 datepicker 插件的文檔，尋找正確的事件名稱

### 問題 2：重複觸發請求
**檢查步驟**：
1. 觀察 Network 標籤中的請求數量
2. 檢查是否有多個監聽器同時觸發

**解決方案**：
- 在 `checkExistingAppointments` 中添加防抖邏輯
- 確保事件監聽器不重複綁定

### 問題 3：日期格式不匹配
**檢查步驟**：
1. 檢查 datepicker 輸出的日期格式
2. 確認後端 API 期望的格式

**解決方案**：
- 在同步時進行日期格式轉換
- 統一使用 YYYY-MM-DD 格式

## 程式碼優化建議

### 防抖處理
```javascript
// 添加防抖，避免頻繁請求
let checkTimeout = null
function debouncedCheck() {
    clearTimeout(checkTimeout)
    checkTimeout = setTimeout(() => {
        this.checkExistingAppointments()
    }, 300)
}
```

### 事件清理
```javascript
// 在組件銷毀時清理事件監聽器
beforeDestroy() {
    $(document).off('change', 'input[name="date"]')
    $(document).off('input', 'input[name="date"]')
    // 清理其他監聽器
}
```

## 成功指標

- [ ] 選擇日期時自動觸發預約檢查
- [ ] 手動輸入日期時正確觸發
- [ ] 手動同步按鈕正常工作
- [ ] 提交前正確同步日期值
- [ ] 不會產生重複的 API 請求
- [ ] 各種 datepicker 插件都能正常工作
- [ ] 沒有 JavaScript 錯誤
- [ ] 效能表現良好

這個解決方案提供了多重保障，確保在各種情況下都能正確捕獲日期變化並觸發相應的檢查邏輯。
