# 輔導預約作業系統實現指南

## 功能概述

已實現輔導預約項目的作業功能，包括：
1. 後台輔導項目設定預設作業
2. 學員申請預約時自動建立作業
3. 前端學員查看和上傳作業
4. 後台管理員查看作業狀況和新增額外作業

## 已完成的修改

### 1. 後台輔導時間設定頁面 (counsel.available_time_reg.tmpl)
**新增功能**：
- 添加作業管理 mixin 和相關 JavaScript
- 新增預設作業設定區塊
- 支援新增、編輯、刪除預設作業
- 作業表單驗證和檔案上傳

**主要修改**：
```html
<!-- 預設作業區塊 -->
<div class="row mb-3">
    <label class="col-md-2 col-form-label text-md-end">預設作業</label>
    <div class="col-md-10 align-self-center">
        <button type="button" @click="showWorkForm()" class="btn btn-sm btn-success">
            <i class="fas fa-plus"></i> 新增作業
        </button>
        <!-- 作業列表顯示 -->
    </div>
</div>
```

### 2. 前端會員輔導頁面 (member.counsel.tmpl)
**新增功能**：
- 添加輔導作業顯示區塊
- 支援作業檔案下載
- 支援作業檔案上傳
- 作業狀態顯示（已繳交/待繳交/已逾期）

**主要修改**：
```html
<!-- 輔導作業區塊 -->
<div class="mb-3" v-if="counselWorks.length > 0">
    <h3>📝 輔導作業</h3>
    <table class="table">
        <!-- 作業列表和操作 -->
    </table>
</div>
```

### 3. 後台輔導管理頁面 (counsel.reg.tmpl)
**新增功能**：
- 添加「新增額外作業」按鈕
- 支援為特定輔導預約新增額外作業
- 整合現有的作業管理功能

**主要修改**：
```html
<button type="button" @click="showAddWorkForm()" class="btn btn-sm btn-success">
    <i class="fas fa-plus"></i> 新增額外作業
</button>
```

## 需要實現的 API 端點

### 1. 輔導項目作業管理 API (後台)
```go
// 輔導項目作業 CRUD
GET    /api/admin/counsel-works              // 獲取輔導項目作業列表
GET    /api/admin/counsel-works/{id}         // 獲取作業詳情
POST   /api/admin/counsel-works              // 創建作業
PATCH  /api/admin/counsel-works/{id}         // 更新作業
DELETE /api/admin/counsel-works/{id}         // 刪除作業
POST   /api/admin/counsel-works/upload/{id}  // 上傳作業檔案
```

### 2. 輔導預約作業管理 API (後台)
```go
// 輔導預約作業管理
GET    /api/admin/counsel-appointments/{id}/works     // 獲取輔導預約的作業列表
POST   /api/admin/counsel-appointments/{id}/works     // 為輔導預約新增額外作業
PATCH  /api/admin/counsel-appointments/works/{work_id} // 更新輔導預約作業
DELETE /api/admin/counsel-appointments/works/{work_id} // 刪除輔導預約作業
```

### 3. 會員輔導作業 API (前台)
```go
// 會員輔導作業查看和上傳
GET  /api/members/counsel-works                    // 獲取會員的輔導作業列表
POST /api/members/counsel-works/{id}/upload        // 上傳作業檔案
```

### 4. 預約時自動建立作業 API
需要修改現有的直接預約 API：
```go
// 在 ApplyCounselDirect 中添加作業建立邏輯
func ApplyCounselDirect(c *gin.Context) {
    // ... 現有邏輯 ...

    // 創建預約成功後，建立預設作業
    if err := createCounselWorks(conn, counselType.ID, appointmentID, memberID); err != nil {
        // 處理錯誤
    }
}
```

## 需要實現的控制器函數

### 1. 後台輔導項目作業控制器
```go
// app/controllers/api/back_api/counsel_work.go
func GetCounselWorks(c *gin.Context)        // 獲取輔導項目作業列表
func GetCounselWork(c *gin.Context)         // 獲取單個作業詳情
func CreateCounselWork(c *gin.Context)      // 創建作業
func UpdateCounselWork(c *gin.Context)      // 更新作業
func DeleteCounselWork(c *gin.Context)      // 刪除作業
func UploadCounselWorkFile(c *gin.Context)  // 上傳作業檔案
```

### 2. 後台輔導預約作業控制器
```go
// app/controllers/api/back_api/counsel_appointment_work.go
func GetCounselAppointmentWorks(c *gin.Context)    // 獲取輔導預約作業列表
func CreateCounselAppointmentWork(c *gin.Context)  // 新增額外作業
func UpdateCounselAppointmentWork(c *gin.Context)  // 更新作業
func DeleteCounselAppointmentWork(c *gin.Context)  // 刪除作業
```

### 3. 前台會員輔導作業控制器
```go
// app/controllers/api/front_api/counsel_work.go
func GetMemberCounselWorks(c *gin.Context)      // 獲取會員輔導作業列表
func UploadMemberCounselWork(c *gin.Context)    // 上傳作業檔案
```

## 資料庫結構

### 1. 輔導項目作業表 (counsel_works)
```sql
CREATE TABLE counsel_works (
    id INT PRIMARY KEY AUTO_INCREMENT,
    counsel_type_id INT NOT NULL,           -- 關聯輔導項目
    work_type CHAR(1) DEFAULT 'C',          -- 作業類型 (C: 輔導單)
    work_title VARCHAR(255),                -- 作業標題
    work_desc TEXT,                         -- 作業說明
    work_file VARCHAR(255),                 -- 作業檔案路徑
    deadline_limit CHAR(1) DEFAULT 'F',     -- 截止日限制 (F: 天數, D: 日期)
    deadline_days INT DEFAULT 7,            -- 截止天數
    deadline_date DATETIME,                 -- 截止日期
    status CHAR(1) DEFAULT 'Y',             -- 狀態 (Y: 啟用, N: 停用)
    created_by_id INT,
    updated_by_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,

    FOREIGN KEY (counsel_type_id) REFERENCES counsel_types(id)
);
```

### 2. 會員輔導作業表 (member_counsel_works)
```sql
CREATE TABLE member_counsel_works (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_id INT NOT NULL,                 -- 會員 ID
    counsel_appointment_id INT NOT NULL,    -- 輔導預約 ID
    counsel_work_id INT DEFAULT NULL,       -- 輔導項目作業 ID (NULL表示額外作業)
    work_type CHAR(1) DEFAULT 'C',          -- 作業類型 (C: 輔導單)
    work_title VARCHAR(255),                -- 作業標題
    work_desc TEXT,                         -- 作業說明
    work_file VARCHAR(255),                 -- 作業檔案路徑
    upload_file VARCHAR(255),               -- 學員上傳檔案路徑
    upload_at DATETIME,                     -- 上傳時間
    deadline_at DATETIME,                   -- 截止時間
    status VARCHAR(20) DEFAULT 'pending',   -- 狀態 (pending, submitted, approved, rejected)
    created_by_id INT,
    updated_by_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,

    FOREIGN KEY (member_id) REFERENCES members(id),
    FOREIGN KEY (counsel_appointment_id) REFERENCES counsel_appointments(id),
    FOREIGN KEY (counsel_work_id) REFERENCES counsel_works(id)
);
```

## 資料模型定義

### 1. 輔導項目作業模型 (CounselWork)
```go
// app/models/counsel_work.go
type CounselWork struct {
    ID            uint      `json:"id" gorm:"primaryKey"`
    CounselTypeID uint      `json:"counsel_type_id" gorm:"not null"`
    WorkType      string    `json:"work_type" gorm:"type:char(1);default:'C'"`
    WorkTitle     string    `json:"work_title" gorm:"type:varchar(255)"`
    WorkDesc      string    `json:"work_desc" gorm:"type:text"`
    WorkFile      string    `json:"work_file" gorm:"type:varchar(255)"`
    DeadlineLimit string    `json:"deadline_limit" gorm:"type:char(1);default:'F'"`
    DeadlineDays  int       `json:"deadline_days" gorm:"default:7"`
    DeadlineDate  *time.Time `json:"deadline_date"`
    Status        string    `json:"status" gorm:"type:char(1);default:'Y'"`
    CreatedByID   *uint     `json:"created_by_id"`
    UpdatedByID   *uint     `json:"updated_by_id"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
    DeletedAt     *time.Time `json:"deleted_at" gorm:"index"`

    // 關聯
    CounselType   CounselType `json:"counsel_type,omitempty" gorm:"foreignKey:CounselTypeID"`
}
```

### 2. 會員輔導作業模型 (MemberCounselWork)
```go
// app/models/member_counsel_work.go
type MemberCounselWork struct {
    ID                    uint      `json:"id" gorm:"primaryKey"`
    MemberID              uint      `json:"member_id" gorm:"not null"`
    CounselAppointmentID  uint      `json:"counsel_appointment_id" gorm:"not null"`
    CounselWorkID         *uint     `json:"counsel_work_id"`
    WorkType              string    `json:"work_type" gorm:"type:char(1);default:'C'"`
    WorkTitle             string    `json:"work_title" gorm:"type:varchar(255)"`
    WorkDesc              string    `json:"work_desc" gorm:"type:text"`
    WorkFile              string    `json:"work_file" gorm:"type:varchar(255)"`
    UploadFile            string    `json:"upload_file" gorm:"type:varchar(255)"`
    UploadAt              *time.Time `json:"upload_at"`
    DeadlineAt            *time.Time `json:"deadline_at"`
    Status                string    `json:"status" gorm:"type:varchar(20);default:'pending'"`
    CreatedByID           *uint     `json:"created_by_id"`
    UpdatedByID           *uint     `json:"updated_by_id"`
    CreatedAt             time.Time `json:"created_at"`
    UpdatedAt             time.Time `json:"updated_at"`
    DeletedAt             *time.Time `json:"deleted_at" gorm:"index"`

    // 關聯
    Member              Member              `json:"member,omitempty" gorm:"foreignKey:MemberID"`
    CounselAppointment  CounselAppointment  `json:"counsel_appointment,omitempty" gorm:"foreignKey:CounselAppointmentID"`
    CounselWork         *CounselWork        `json:"counsel_work,omitempty" gorm:"foreignKey:CounselWorkID"`

    // 額外欄位
    CounselTitle        string `json:"counsel_title,omitempty" gorm:"-"`
}
```

## 實現流程

### 1. 後台設定預設作業
1. 管理員在輔導項目設定頁面新增預設作業
2. 設定作業標題、說明、檔案、截止日規則
3. 作業資料儲存到 `counsel_works` 表

### 2. 學員申請預約時建立作業
1. 學員選擇輔導項目並申請預約
2. 系統查詢該輔導項目的預設作業
3. 為每個預設作業建立對應的會員作業記錄
4. 計算截止日期並儲存到 `member_counsel_works` 表

### 3. 學員查看和上傳作業
1. 學員在輔導頁面查看所有輔導作業
2. 可以下載作業檔案
3. 可以上傳完成的作業檔案
4. 查看作業狀態和截止日期

### 4. 後台管理作業
1. 管理員在輔導管理頁面查看學員作業狀況
2. 可以調整作業截止日期
3. 可以審核作業狀態
4. 可以為特定預約新增額外作業

## 測試步驟

### 第一步：後台預設作業設定測試
1. 訪問 `/admin/counsel/available-times/reg`
2. 新增一個輔導項目
3. 在預設作業區塊點擊「新增作業」
4. 填寫作業資訊並上傳檔案
5. 確認作業正確儲存

### 第二步：學員預約建立作業測試
1. 學員訪問 `/member/counsel`
2. 選擇有預設作業的輔導項目
3. 申請預約
4. 確認系統自動建立對應的會員作業

### 第三步：學員作業操作測試
1. 學員在輔導頁面查看作業列表
2. 下載作業檔案
3. 上傳完成的作業
4. 確認狀態正確更新

### 第四步：後台作業管理測試
1. 管理員訪問 `/admin/counsel/reg?id={預約ID}`
2. 查看學員作業上傳狀況
3. 點擊「新增額外作業」
4. 為該預約新增額外作業

## 成功指標

- [ ] 後台可以為輔導項目設定預設作業
- [ ] 學員申請預約時自動建立作業
- [ ] 前端正確顯示輔導作業列表
- [ ] 學員可以下載和上傳作業檔案
- [ ] 作業狀態正確顯示
- [ ] 後台可以查看作業上傳狀況
- [ ] 後台可以新增額外作業
- [ ] 所有 API 端點正常運作
- [ ] 資料庫記錄正確建立和更新

## 後續優化建議

### 1. 功能增強
- 添加作業評分功能
- 支援多種作業類型
- 添加作業提醒通知
- 支援作業批量操作

### 2. 使用者體驗
- 添加作業進度顯示
- 支援作業預覽功能
- 優化檔案上傳體驗
- 添加作業討論功能

### 3. 管理功能
- 添加作業統計報表
- 支援作業模板管理
- 添加作業審核流程
- 支援作業批量評分

這個作業系統為輔導預約功能提供了完整的作業管理能力，大幅提升了輔導服務的專業性和系統性。
