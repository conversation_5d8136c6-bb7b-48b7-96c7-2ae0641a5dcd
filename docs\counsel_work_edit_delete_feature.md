# 輔導作業修改/刪除功能實現總結

## 🎯 功能目標

為額外輔導作業添加修改和刪除功能，讓管理員可以對額外作業進行完整的 CRUD 操作，同時保護預設作業不被誤刪或修改。

## ✅ 已完成的實現

### 1. 表格操作欄位增強

#### 操作按鈕組設計
```html
<div class="btn-group" role="group">
    <!-- 查看詳情 -->
    <button type="button" @click="showCounselWorkDetail(counselWork)"
            class="btn btn-sm btn-outline-primary" title="查看詳情">
        <i class="fas fa-eye"></i>
    </button>
    
    <!-- 修改按鈕 - 只有額外作業可以修改 -->
    <button type="button" 
            v-if="!counselWork.counsel_work_id"
            @click="editCounselWork(counselWork)"
            class="btn btn-sm btn-outline-warning" title="修改作業">
        <i class="fas fa-edit"></i>
    </button>
    
    <!-- 刪除按鈕 - 只有額外作業可以刪除 -->
    <button type="button" 
            v-if="!counselWork.counsel_work_id"
            @click="deleteCounselWork(counselWork)"
            class="btn btn-sm btn-outline-danger" title="刪除作業">
        <i class="fas fa-trash"></i>
    </button>
</div>
```

#### 權限控制邏輯
- **預設作業** (`counsel_work_id` 有值)：只能查看，不能修改或刪除
- **額外作業** (`counsel_work_id` 為空)：可以查看、修改、刪除

### 2. JavaScript 方法實現

#### 修改作業方法
```javascript
editCounselWork(counselWork) {
    // 只有額外作業可以修改
    if (counselWork.counsel_work_id) {
        msgError('預設作業無法修改')
        return
    }
    
    // 填入現有資料
    this.counselWorkItem = {
        id: counselWork.id,
        counsel_appointment_id: counselWork.counsel_appointment_id,
        work_title: counselWork.work_title,
        work_desc: counselWork.work_desc,
        work_file: counselWork.work_file,
        deadline_at: this.formatDateTimeForInput(counselWork.deadline_at),
        status: counselWork.status,
        file: null,
    }
    
    // 顯示表單
    $.fancybox.open({
        src: '#counsel_work_form',
        type: 'inline',
        opts: {
            toolbar: false,
            touch: false,
            smallBtn: true,
            clickSlide: false,
            clickOutside: false,
        },
    })
}
```

#### 刪除作業方法
```javascript
deleteCounselWork(counselWork) {
    // 只有額外作業可以刪除
    if (counselWork.counsel_work_id) {
        msgError('預設作業無法刪除')
        return
    }
    
    msgDeleteConfirm(`確定要刪除作業「${counselWork.work_title}」嗎？`, () => {
        axiosRequest()
            .delete(`/api/admin/counsel-appointments/works/${counselWork.id}`)
            .then(res => {
                msgTopSuccess('作業刪除成功')
                this.getCounselWorks() // 重新載入作業列表
            })
            .catch(err => {
                console.log(err)
                msgError(err.response?.data?.msg || '刪除失敗')
            })
    })
}
```

#### 統一的表單提交方法
```javascript
async submitCounselWorkForm() {
    if (!$('#counsel_work_form_inner').valid())
        return

    msgLoading()

    try {
        const workData = {
            counsel_appointment_id: this.counselWorkItem.counsel_appointment_id,
            work_title: this.counselWorkItem.work_title,
            work_desc: this.counselWorkItem.work_desc,
            deadline_at: this.formatFullDate(this.counselWorkItem.deadline_at),
            status: this.counselWorkItem.status,
        }

        let response
        let successMsg
        
        if (this.counselWorkItem.id) {
            // 修改作業
            response = await axiosRequest('json')
                .patch(`/api/admin/counsel-appointments/works/${this.counselWorkItem.id}`, workData)
            successMsg = '輔導作業修改成功'
        } else {
            // 新增作業
            response = await axiosRequest('json')
                .post(`/api/admin/counsel-appointments/${this.item.id}/works`, workData)
            successMsg = '額外輔導作業新增成功'
        }

        // 如果有檔案，上傳檔案
        if (this.counselWorkItem.file) {
            const workId = this.counselWorkItem.id || response.data.id
            await this.uploadCounselWorkFile(workId)
        }

        this.closeCounselWorkForm()
        this.getCounselWorks() // 重新載入作業列表
        msgTopSuccess(successMsg)
    } catch (err) {
        console.log(err)
        msgError(err.response?.data?.msg || '操作失敗')
    }
}
```

### 3. 輔助方法

#### 日期時間格式化方法
```javascript
formatDateTimeForInput(dateTimeStr) {
    if (!dateTimeStr) return ''
    try {
        const date = new Date(dateTimeStr)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day}T${hours}:${minutes}`
    } catch (e) {
        return ''
    }
}
```

### 4. 表單界面優化

#### 動態標題顯示
```html
<div class="card-header">
    <span v-if="counselWorkItem.id">修改額外輔導作業</span>
    <span v-else>新增額外輔導作業</span>
    - 以下*欄位為必填欄位
</div>
```

#### 動態按鈕文字
```html
<button type="submit" class="btn btn-success me-2">
    <i class="fas fa-save" v-if="counselWorkItem.id"></i>
    <i class="fas fa-plus" v-else></i>
    <span v-if="counselWorkItem.id">修改作業</span>
    <span v-else>新增作業</span>
</button>
```

## 🔧 技術實現特點

### 1. 權限控制機制
- **資料層面**：通過 `counsel_work_id` 欄位區分預設作業和額外作業
- **界面層面**：使用 `v-if` 條件渲染控制按鈕顯示
- **操作層面**：在方法中進行二次檢查，防止誤操作

### 2. 表單復用設計
- **單一表單**：新增和修改使用同一個表單
- **動態內容**：根據 `counselWorkItem.id` 判斷操作類型
- **資料預填**：修改時自動填入現有資料

### 3. 用戶體驗優化
- **確認對話框**：刪除操作需要用戶確認
- **即時反饋**：操作成功後立即更新列表
- **錯誤處理**：友善的錯誤訊息提示

### 4. 資料格式處理
- **輸入格式**：將資料庫日期時間轉換為 HTML datetime-local 格式
- **輸出格式**：將表單資料轉換為後端需要的格式
- **檔案處理**：支援修改時重新上傳檔案

## 📊 功能流程

### 1. 修改作業流程
```
1. 點擊修改按鈕（只有額外作業顯示）
2. 檢查作業類型權限
3. 載入現有資料到表單
4. 顯示彈出式表單（標題顯示「修改」）
5. 用戶修改資料
6. 提交表單進行驗證
7. 發送 PATCH 請求更新作業
8. 上傳新檔案（如果有）
9. 關閉表單並重新載入列表
10. 顯示成功訊息
```

### 2. 刪除作業流程
```
1. 點擊刪除按鈕（只有額外作業顯示）
2. 檢查作業類型權限
3. 顯示確認對話框
4. 用戶確認刪除
5. 發送 DELETE 請求
6. 重新載入作業列表
7. 顯示成功訊息
```

## 🎯 安全性考量

### 1. 前端權限控制
- 按鈕顯示控制：預設作業不顯示修改/刪除按鈕
- 方法內檢查：即使調用方法也會檢查權限
- 用戶確認：刪除操作需要用戶明確確認

### 2. 後端 API 保護
- 使用現有的 API 端點，已有適當的權限檢查
- PATCH 和 DELETE 請求都會驗證作業存在性
- 管理員身份驗證確保操作安全性

### 3. 資料完整性
- 表單驗證確保必填欄位不為空
- 日期格式驗證確保資料正確性
- 檔案上傳檢查確保檔案安全性

## 📋 使用者界面特點

### 1. 直觀的操作設計
- **按鈕分組**：相關操作按鈕組合在一起
- **圖示提示**：清楚的圖示表示不同操作
- **工具提示**：滑鼠懸停顯示操作說明

### 2. 一致的視覺風格
- **顏色編碼**：查看（藍色）、修改（黃色）、刪除（紅色）
- **按鈕大小**：統一的小尺寸按鈕
- **間距設計**：適當的按鈕間距

### 3. 響應式互動
- **即時反饋**：操作後立即顯示結果
- **載入狀態**：提交時顯示載入提示
- **錯誤處理**：清楚的錯誤訊息

## 📋 測試檢查清單

### 1. 權限控制測試
- [ ] 預設作業不顯示修改/刪除按鈕
- [ ] 額外作業正確顯示所有操作按鈕
- [ ] 直接調用方法時的權限檢查

### 2. 修改功能測試
- [ ] 修改表單正確載入現有資料
- [ ] 表單標題和按鈕文字正確顯示
- [ ] 修改提交成功並更新列表
- [ ] 檔案重新上傳功能正常

### 3. 刪除功能測試
- [ ] 刪除確認對話框正常顯示
- [ ] 確認刪除後作業正確移除
- [ ] 取消刪除時不執行操作
- [ ] 刪除後列表正確更新

### 4. 錯誤處理測試
- [ ] 網路錯誤時的處理
- [ ] 權限錯誤時的提示
- [ ] 表單驗證失敗的處理

## 🚀 改善效果

### 1. 功能完整性
- ✅ 完整的 CRUD 操作支援
- ✅ 智能的權限控制機制
- ✅ 統一的操作界面設計

### 2. 使用者體驗
- ✅ 直觀的操作流程
- ✅ 清楚的視覺反饋
- ✅ 友善的錯誤處理

### 3. 系統安全性
- ✅ 多層次的權限檢查
- ✅ 用戶操作確認機制
- ✅ 完整的資料驗證

### 4. 維護性
- ✅ 代碼復用性高
- ✅ 邏輯清晰易懂
- ✅ 擴展性良好

這個實現為輔導作業系統提供了完整的管理功能，讓管理員可以靈活地管理額外作業，同時保護預設作業的完整性。整個功能設計考慮了安全性、易用性和維護性，為用戶提供了優秀的操作體驗。
