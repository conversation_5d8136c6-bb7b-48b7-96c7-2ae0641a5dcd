# 綠界金流管理後台設定說明

## 如何在管理後台添加綠界金流支付選項

### 1. 進入支付選項管理

1. 登入管理後台
2. 進入「訂單管理」→「付款方式」
3. 點擊「新增」按鈕

### 2. 設定綠界金流支付選項

#### 基本設定

- **名稱**: 綠界金流 (或自定義名稱)
- **支付方式**: 選擇 `ecpay`
- **狀態**: 啟用 (Y)

#### 綠界金流設定

##### 測試環境設定

- **是否模擬**: 勾選 ✓
- **商戶ID**: `3366242` (測試用)
- **HashKey**: `aHk46nU5rBAIS7kp` (測試用)
- **HashIV**: `hpOOGBqq2jracs3x` (測試用)

##### 正式環境設定

- **是否模擬**: 不勾選
- **商戶ID**: 您的綠界商戶編號
- **HashKey**: 您的綠界 HashKey
- **HashIV**: 您的綠界 HashIV

#### 其他設定

- **付款類型**: CREDIT (信用卡)
- **備註**: 可填寫相關說明

### 3. 測試流程

#### 測試環境測試

1. 確保設定為模擬模式
2. 在前端選擇儲值方案
3. 選擇綠界金流付款
4. 使用綠界提供的測試卡號進行測試

#### 測試卡號資訊

- **信用卡號**: 4311-9522-2222-2222
- **安全碼**: 222
- **有效期限**: 任意未來日期

### 4. 正式環境部署

1. 向綠界申請正式商戶帳號
2. 取得正式的商戶ID、HashKey、HashIV
3. 更新支付選項設定為正式環境參數
4. 關閉模擬模式
5. 進行小額測試確認功能正常

### 5. 監控和維護

#### 交易記錄查看

- 進入「訂單管理」→「訂單列表」
- 可查看綠界金流的交易狀態
- 檢查 `ecpay_orders` 表的交易記錄

#### 常見問題排查

1. **付款失敗**
   - 檢查 HashKey 和 HashIV 是否正確
   - 確認網域設定是否正確
   - 查看錯誤日誌

2. **通知未收到**
   - 確認通知 URL 是否可訪問
   - 檢查防火牆設定
   - 查看綠界後台的通知記錄

3. **點數未配發**
   - 檢查訂單狀態更新
   - 確認通知處理邏輯
   - 查看點數歷史記錄

### 6. 安全注意事項

1. **保護敏感資訊**
   - HashKey 和 HashIV 不可外洩
   - 定期更換密鑰
   - 使用 HTTPS 連線

2. **驗證機制**
   - 實現完整的 CheckMacValue 驗證
   - 檢查交易金額是否正確
   - 防止重複通知處理

3. **日誌記錄**
   - 記錄所有交易相關操作
   - 保留足夠的日誌資訊供查詢
   - 定期備份重要資料

### 7. 支援聯絡

如有技術問題，請聯絡：
- 綠界技術支援
- 系統開發團隊
