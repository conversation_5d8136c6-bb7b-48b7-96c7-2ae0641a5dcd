# 輔導時間設定整合管理功能測試指南

## 功能概述

已成功將常駐時間設定整合到現有的可預約時間管理系統中，提供統一的管理介面，避免使用者需要在多個頁面間切換。

### 主要改進

1. **統一資料模型**：使用 `counsel_available_times` 表格同時管理固定日期和常駐設定
2. **整合管理介面**：在同一個頁面管理兩種類型的時間設定
3. **智能篩選**：根據類型動態顯示相應的搜尋條件
4. **優先級邏輯**：保持固定日期優先於常駐設定的邏輯

### 資料模型變更

#### 新增欄位
```sql
ALTER TABLE `counsel_available_times` 
ADD COLUMN `type` ENUM('specific', 'recurring') NOT NULL DEFAULT 'specific',
ADD COLUMN `day_of_week` TINYINT(1) NULL;
```

#### 約束變更
- 移除原來的 `unique_date` 約束
- 新增 `unique_specific_date` 約束（針對固定日期）
- 新增 `unique_recurring_day` 約束（針對常駐設定）

## 測試步驟

### 第一步：資料庫遷移測試

```bash
# 執行遷移
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" up

# 檢查表格結構
mysql -u username -p database_name -e "DESCRIBE counsel_available_times;"

# 檢查預設常駐資料
mysql -u username -p database_name -e "SELECT type, day_of_week, time_slots, status, note FROM counsel_available_times WHERE type = 'recurring' ORDER BY day_of_week;"
```

**預期結果**：
- 新增 `type` 和 `day_of_week` 欄位
- 包含星期一到星期六的預設常駐設定
- 約束正確設定

### 第二步：整合管理介面測試

#### 2.1 可預約時間列表頁面
1. 訪問 `/admin/counsel/available-times`
2. **檢查新功能**：
   - 類型篩選下拉選單（全部類型、固定日期、常駐設定）
   - 根據類型動態顯示搜尋條件
   - 表格新增「類型」欄位
   - 「日期/星期」欄位根據類型顯示不同內容

#### 2.2 類型篩選測試
1. **全部類型**：顯示所有固定日期和常駐設定
2. **固定日期**：只顯示固定日期設定，隱藏星期篩選
3. **常駐設定**：只顯示常駐設定，隱藏日期範圍篩選

#### 2.3 表格顯示測試
- 固定日期：類型顯示藍色「固定日期」標籤，日期/星期欄位顯示具體日期
- 常駐設定：類型顯示藍綠色「常駐設定」標籤，日期/星期欄位顯示星期名稱

### 第三步：編輯頁面測試

#### 3.1 新增功能測試
1. 點擊「新增可預約時間」
2. **檢查新功能**：
   - 類型選擇下拉選單（固定日期、常駐設定）
   - 根據類型動態顯示相應欄位
   - 表單驗證根據類型調整

#### 3.2 固定日期設定測試
1. 選擇「固定日期」類型
2. 檢查日期欄位顯示，星期欄位隱藏
3. 設定特定日期和時間段
4. 提交並檢查成功

#### 3.3 常駐設定測試
1. 選擇「常駐設定」類型
2. 檢查星期欄位顯示，日期欄位隱藏
3. 選擇星期日（預設沒有設定）
4. 設定時間段：10:00-16:00
5. 提交並檢查成功

#### 3.4 重複檢查測試
1. 嘗試新增已存在的固定日期
2. 嘗試新增已存在的常駐星期
3. 檢查錯誤訊息正確顯示

### 第四步：前端整合測試

#### 4.1 可預約日期生成測試
1. 訪問 `/member/counsel`
2. 點擊「直接預約輔導」
3. **檢查項目**：
   - 日期下拉選單包含固定日期和常駐生成的日期
   - 常駐生成的日期備註顯示「常駐設定 - ...」
   - 優先級邏輯正確（固定日期覆蓋常駐設定）

#### 4.2 時間段顯示測試
1. 選擇固定日期設定的日期
2. 檢查時間段正確顯示
3. 選擇常駐設定生成的日期
4. 檢查時間段正確顯示

### 第五步：API 測試

#### 5.1 後台管理 API
```bash
# 獲取所有類型的設定
curl -X GET "/api/admin/counsel-available-times"

# 篩選固定日期
curl -X GET "/api/admin/counsel-available-times?search[type]=specific"

# 篩選常駐設定
curl -X GET "/api/admin/counsel-available-times?search[type]=recurring"

# 新增固定日期設定
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "specific",
    "date": "2025-08-25",
    "time_slot_options": [
      {"start": "10:00", "end": "15:00"}
    ],
    "status": "Y",
    "note": "特殊活動日"
  }'

# 新增常駐設定
curl -X POST "/api/admin/counsel-available-times" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "recurring",
    "day_of_week": 0,
    "time_slot_options": [
      {"start": "10:00", "end": "16:00"}
    ],
    "status": "Y",
    "note": "星期日特殊時間"
  }'
```

#### 5.2 前端可預約日期 API
```bash
# 獲取可預約日期（整合固定和常駐）
curl -X GET "/api/members/counsels/available-dates"

# 獲取指定日期的時間段
curl -X GET "/api/members/counsels/available-times/2025-08-25"
```

### 第六步：資料一致性測試

#### 6.1 優先級邏輯測試
1. 設定星期一的常駐時間：09:00-17:00
2. 為特定星期一設定固定日期：10:00-15:00
3. 檢查前端顯示固定日期設定（優先級正確）

#### 6.2 資料完整性測試
1. 檢查固定日期的 `day_of_week` 欄位為 NULL
2. 檢查常駐設定的 `date` 欄位為 NULL
3. 檢查約束正確防止重複

#### 6.3 遷移回滾測試
```bash
# 測試回滾
migrate -path database/migrations -database "mysql://connection" down 1

# 檢查表格恢復原狀
mysql -u username -p database_name -e "DESCRIBE counsel_available_times;"
```

### 第七步：使用者體驗測試

#### 7.1 管理員工作流程
1. **設定常駐時間**：選擇常駐設定類型，設定工作日時間
2. **調整特殊日期**：選擇固定日期類型，設定節假日時間
3. **查看整體設定**：使用篩選功能查看不同類型的設定

#### 7.2 介面友善性
- 類型切換時欄位動態顯示/隱藏
- 錯誤訊息清楚明確
- 說明文字幫助理解功能

## 效能考量

### 1. 查詢效能
- 使用 `type` 欄位索引提升篩選效能
- 分別查詢固定日期和常駐設定，避免複雜 JOIN

### 2. 資料完整性
- 約束確保資料一致性
- 類型欄位防止資料混亂

### 3. 前端效能
- 智能篩選減少不必要的資料載入
- 動態欄位顯示提升使用者體驗

## 管理建議

### 1. 使用策略
- 先設定常駐時間作為基礎
- 針對特殊日期使用固定日期設定
- 定期檢查和清理過期的固定日期

### 2. 維護流程
- 每週檢查下週的時間設定
- 節假日前設定特殊時間
- 使用類型篩選快速定位設定

### 3. 培訓重點
- 向管理員說明兩種類型的差異
- 強調優先級邏輯
- 提供最佳實踐指南

## 成功指標

- [ ] 資料庫遷移成功
- [ ] 整合管理介面正常運作
- [ ] 類型篩選功能正常
- [ ] 動態欄位顯示正確
- [ ] 新增/編輯功能支援兩種類型
- [ ] 前端整合正常
- [ ] 優先級邏輯正確
- [ ] API 回應格式正確
- [ ] 使用者體驗良好
- [ ] 管理員工作效率提升

這個整合方案成功將兩種時間設定類型統一管理，大幅提升了管理效率和使用者體驗，同時保持了系統的靈活性和可維護性。
