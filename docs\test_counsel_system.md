# 輔導預約系統優化測試指南

## 系統概述

本次優化將輔導預約系統從基於道具卡的機制改為支援直接預約的方式，同時保持向後兼容性。

## 主要變更

### 1. 資料庫變更
- 新增 `counsel_types` 表格用於管理輔導項目
- 在 `counsel_appointments` 表格中新增直接預約相關欄位：
  - `counsel_type_id`: 輔導項目ID
  - `appointment_date`: 預約日期
  - `appointment_time`: 預約時間
  - `requested_duration`: 請求的輔導時長

### 2. 後台管理功能
- 新增輔導項目管理頁面：`/admin/counsel/types`
- 支援新增、編輯、刪除輔導項目
- 可設定項目名稱、描述、時長範圍等

### 3. 前端預約功能
- 新增「直接預約輔導」按鈕
- 支援選擇輔導項目、日期、時間、時長
- 可選擇關聯特定課程

## 測試步驟

### 1. 資料庫遷移測試
```bash
# 執行遷移
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/database" up

# 檢查表格是否正確創建
# 應該看到 counsel_types 表格和 counsel_appointments 的新欄位
```

### 2. 後台管理測試

#### 2.1 輔導項目管理
1. 訪問 `/admin/counsel/types`
2. 檢查是否顯示預設的輔導項目（A輔導、B輔導、專業諮詢）
3. 測試新增輔導項目：
   - 點擊「新增輔導項目」
   - 填寫項目名稱、描述、時長設定
   - 檢查驗證規則（最小時長不能大於最大時長等）
4. 測試編輯功能
5. 測試刪除功能

#### 2.2 後台預約管理
1. 訪問 `/admin/counsel/index`
2. 檢查是否正確顯示新的預約類型（direct/goods）
3. 檢查新欄位是否正確顯示

### 3. 前端預約測試

#### 3.1 直接預約功能
1. 訪問 `/member/counsel`
2. 檢查是否顯示「直接預約輔導」按鈕
3. 點擊按鈕測試預約流程：
   - 選擇輔導項目
   - 選擇日期（應該限制不能選擇過去日期）
   - 選擇時間
   - 調整時長（應該在允許範圍內）
   - 可選擇關聯課程
4. 提交預約並檢查是否成功

#### 3.2 舊有功能兼容性
1. 測試原有的「課程輔導預約」功能
2. 確保道具卡預約機制仍然正常運作
3. 檢查預約歷史記錄顯示

### 4. API 測試

#### 4.1 前端 API
```bash
# 獲取輔導項目列表
curl -X GET "/api/members/counsels/types" \
  -H "Authorization: Bearer <token>"

# 直接預約
curl -X POST "/api/members/counsels/direct" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "counsel_type_id": 1,
    "appointment_date": "2024-01-15",
    "appointment_time": "14:00",
    "requested_duration": 60,
    "mem_pro_id": 0
  }'
```

#### 4.2 後台 API
```bash
# 獲取輔導項目列表
curl -X GET "/api/admin/counsel-types"

# 新增輔導項目
curl -X POST "/api/admin/counsel-types" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "測試輔導",
    "description": "測試用輔導項目",
    "default_duration": 60,
    "min_duration": 30,
    "max_duration": 120,
    "status": "Y",
    "sorting": 10
  }'
```

## 預期結果

### 成功指標
1. 資料庫遷移無錯誤
2. 後台可以正常管理輔導項目
3. 前端可以進行直接預約
4. 舊有道具卡預約功能仍然正常
5. 所有 API 回應正確的資料格式
6. 預約記錄正確儲存到資料庫

### 錯誤處理
1. 輸入驗證正確運作
2. 權限檢查正常
3. 錯誤訊息清楚明確

## 注意事項

1. **向後兼容性**：確保現有的道具卡預約功能不受影響
2. **資料完整性**：新舊預約記錄都能正確顯示
3. **使用者體驗**：新功能應該直觀易用
4. **效能**：確保新功能不影響系統效能

## 回滾計劃

如果發現重大問題，可以執行以下回滾步驟：
1. 回滾資料庫遷移：`migrate down 2`
2. 恢復原始程式碼
3. 重新部署

## 後續優化建議

1. 添加預約時間衝突檢查
2. 實作預約提醒功能
3. 添加預約統計報表
4. 考慮添加預約取消功能
